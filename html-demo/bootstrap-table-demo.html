<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bootstrap Table with Column Resizing</title>
  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <!-- Bootstrap Table CSS -->
  <link rel="stylesheet" href="https://unpkg.com/bootstrap-table@1.18.3/dist/bootstrap-table.min.css">
  <style>
    .grip {
      width: 5px;
      height: 20px;
      background-color: #ccc;
      cursor: col-resize;
    }
    .dragging {
      background-color: #f0f0f0;
    }
  </style>
</head>
<body>
<table id="table" data-toggle="table" data-url="data.json">
  <thead>
  <tr>
    <th data-field="id">ID</th>
    <th data-field="name">Name</th>
    <th data-field="price">Price</th>
  </tr>
  </thead>
</table>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<!-- Bootstrap JS -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<!-- Bootstrap Table JS -->
<script src="https://unpkg.com/bootstrap-table@1.18.3/dist/bootstrap-table.min.js"></script>
<!-- colResizable JS -->
<script src="colResizable-1.6.min.js"></script>
<style>
  .grip {
    width: 5px;
    height: 20px;
    background-color: #ccc;
    cursor: col-resize;
  }
  .dragging {
    background-color: #f0f0f0;
  }
</style>
<script>
  $(function() {
    // 初始化 bootstrap-table
    $('#table').bootstrapTable();

    // 初始化 colResizable
    $('#table').colResizable({
      resizeMode:'flex',
      liveDrag: true,
      gripInnerHtml: "<div class='grip'></div>",
      draggingClass: "dragging",
      onResize: function() {
        // 列宽调整时的回调函数
        $(this).bootstrapTable('resetView');
      }
    });
  });
</script>
</body>
</html>