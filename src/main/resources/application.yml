server:
  port: 80

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # 可以不配置，根据url自动识别，建议配置
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      url: **********************************************************************************************
      username: root
      password: 123456
      # 初始化连接池个数
      initial-size: 5
      # 最大连接池个数
      max-active: 20
      # 最小连接池个数
      min-idle: 5
      # 配置获取连接等待超时的时间，单位毫秒，缺省启用公平锁，并发效率会有所下降
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会起作用
      validation-query: SELECT 1 FROM DUAL
      # 建议配置为true，不影响性能，并且保证安全性。
      # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
  #  data:
  #    mongodb:
  #      uri: mongodb://rwuser:rIcK15J4HB!meD@***********:7122/test?authSource=admin
  #Thymaleaf 视图模板引擎配置
  thymeleaf:
    #启用模板缓存 页面缓存设置（默认为true），开发中方便调试应设置为false，上线稳定后应保持默认true
    cache: false
    #模板编码
    encoding: UTF-8
    #应用于模板的模板样式
    mode: HTML5
    #指定模板页面存放路径
    prefix: classpath:/templates/
    #指定模板页面名称的后缀
    suffix: .html
    #Redis 配置
  redis:
    host: 127.0.0.1
    port: 6379
    #    password: root
    jedis:
      pool:
        max-idle: 8
        min-idle: 0
        max-active: 8
        max-wait: -1
    timeout: 0

#kafka:
#  broker-list: *************:9094,************:9094,************:9094
#  topics: test
#  consumer:
#    gourp-id: 1
#    enable-auto-commit: true
#    auto-commit-ms: 1000
#    session-timeout-ms: 10000
#    key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#    value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

jasypt:
  encryptor:
    password: mesopal


