<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
    "-//Hibernate/Hibernate Configuration DTD//EN"
    "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
  <session-factory>
    <!--数据库连接url配置-->
    <property name="connection.url">**********************************</property>
    <!--数据库驱动配置-->
    <property name="connection.driver_class">com.mysql.jdbc.Driver</property>
    <!--数据库用户名配置-->
    <property name="connection.username">root</property>
    <!--数据库密码配置-->
    <property name="connection.password">Mes_opa123</property>

    <!-- DB schema will be updated if needed -->
    <!-- <property name="hbm2ddl.auto">update</property> -->
  </session-factory>
</hibernate-configuration>