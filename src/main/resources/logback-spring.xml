<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义全局变量，日志文件路径和格式 -->
    <property name="log.path" value="${LOG_PATH}"/>
    <property name="log.name" value="${LOG_FILE}"/>
    <property name="file.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{50} - %msg%n"/>

    <!-- 控制台输出配置 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${file.pattern}</pattern>
        </encoder>
        <!-- 只输出DEBUG级别及以上的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- 文件输出配置 -->
<!--    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        <file>${log.path}/${log.name}.info.log</file>-->
<!--        <encoder>-->
<!--            <pattern>${file.pattern}</pattern>-->
<!--        </encoder>-->
<!--        &lt;!&ndash; 只输出INFO级别的日志 &ndash;&gt;-->
<!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--            <level>INFO</level>-->
<!--            <onMatch>ACCEPT</onMatch>-->
<!--            <onMismatch>DENY</onMismatch>-->
<!--        </filter>-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
<!--            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/${log.name}.info.%d{yyyy-MM-dd}-%i.log</fileNamePattern>-->
<!--            &lt;!&ndash; 每个文件最大10MB &ndash;&gt;-->
<!--            <maxFileSize>10MB</maxFileSize>-->
<!--            &lt;!&ndash; 最多保留30天的历史记录 &ndash;&gt;-->
<!--            <maxHistory>30</maxHistory>-->
<!--        </rollingPolicy>-->
<!--    </appender>-->

    <!-- 文件输出配置 -->
<!--    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        <file>${log.path}/${log.name}.error.log</file>-->
<!--        <encoder>-->
<!--            <pattern>${file.pattern}</pattern>-->
<!--        </encoder>-->
<!--        &lt;!&ndash; 只输出ERROR级别的日志 &ndash;&gt;-->
<!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--            <level>ERROR</level>-->
<!--            <onMatch>ACCEPT</onMatch>-->
<!--            <onMismatch>DENY</onMismatch>-->
<!--        </filter>-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
<!--            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/${log.name}.error.%d{yyyy-MM-dd}-%i.log</fileNamePattern>-->
<!--            &lt;!&ndash; 每个文件最大10MB &ndash;&gt;-->
<!--            <maxFileSize>10MB</maxFileSize>-->
<!--            &lt;!&ndash; 最多保留30天的历史记录 &ndash;&gt;-->
<!--            <maxHistory>30</maxHistory>-->
<!--        </rollingPolicy>-->
<!--    </appender>-->

    <!-- 设置root logger -->
    <root level="DEBUG">
        <appender-ref ref="STDOUT"/>
<!--        <appender-ref ref="INFO_FILE"/>-->
<!--        <appender-ref ref="ERROR_FILE"/>-->
    </root>

</configuration>
