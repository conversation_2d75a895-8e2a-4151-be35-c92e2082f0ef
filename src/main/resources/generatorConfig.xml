<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<!-- 配置Run As Maven build : Goals 参数 : mybatis-generator:generate -Dmybatis.generator.overwrite=true -->
<!-- 配置 tableName,使用 Run As Maven build 生成 dao model 层 -->
<generatorConfiguration>
    <!-- 配置文件路径 -->
    <properties resource="generatorConfig.properties"/>

    <context id="DB2Tables" targetRuntime="MyBatis3"
             defaultModelType="flat">

        <!--自定义生成PO注释-->
        <commentGenerator type="com.demo.mybatis.MybatisCommentGenerator">
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>

        <!--数据库连接信息 -->
        <jdbcConnection driverClass="${jdbc.driver}"
                        connectionURL="${jdbc.url}" userId="${jdbc.username}" password="${jdbc.password}">
        </jdbcConnection>

        <!--自定义生成PO将tinyint转为Integer-->
        <javaTypeResolver type="com.demo.mybatis.MybatisJavaTypeResolverDefaultImpl">
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!--生成的model 包路径 -->
        <javaModelGenerator targetPackage="${model.package}"
                            targetProject="${use.code.target.project}">
            <property name="enableSubPackages" value="ture"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!--生成xml mapper文件 路径 -->
        <sqlMapGenerator targetPackage="${xml.mapper.package}"
                         targetProject="${use.code.targetResource.project}">
            <property name="enableSubPackages" value="ture"/>
        </sqlMapGenerator>

        <!-- 生成的Dao接口 的包路径 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="${dao.package}" targetProject="${use.code.target.project}">
            <property name="enableSubPackages" value="ture"/>
        </javaClientGenerator>

        <!--站位代码-->
        <table tableName="T_AP_CONTENTTEMPLATE" mapperName="CONTENTTEMPLATEMapper" domainObjectName="CONTENTTEMPLATE"
               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="false"/>
        </table>
    </context>
</generatorConfiguration>