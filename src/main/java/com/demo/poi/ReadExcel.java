//package com.demo.poi;
//
//import org.apache.poi.ss.usermodel.Cell;
//import org.apache.poi.ss.usermodel.Row;
//import org.apache.poi.ss.usermodel.Sheet;
//import org.apache.poi.ss.usermodel.Workbook;
//import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//
//import java.io.InputStream;
//
///**
// * 导入Excel
// */
//public class ReadExcel {
//
//    public Workbook workbook;
//
//    public Sheet sheet;
//
//    public Row row;
//
//    public void read(InputStream inputStream) throws Exception {
//        workbook = new XSSFWorkbook(inputStream);//获取工作簿
//        int numberOfSheets = workbook.getNumberOfSheets();//获取工作表页数
//        /* 循环获取工作表 */
//        for (int i = 0; i < numberOfSheets; i++) {
//            getSheet(i);
//        }
//        workbook.write();
//
//    }
//
//    private void getSheet(int index) {
//        if (workbook == null) {
//            return;
//        }
//        sheet = workbook.getSheetAt(index);
//        int countRows = sheet.getPhysicalNumberOfRows();//获取总行数
//        int first = sheet.getFirstRowNum();//第一个行号
//        int last = sheet.getLastRowNum();//最后一个行号
//        for (int i = first; i <= last; i++) {
//            getRow(i);
//        }
//    }
//
//    private void getRow(int index) {
//        if (sheet == null) {
//            return;
//        }
//
//        row = sheet.getRow(index);
//        int a = row.getPhysicalNumberOfCells();
//        if (row == null) {
//            return;
//        }
//        int countCell = row.getPhysicalNumberOfCells();//获取总列数
//        for (int i = 0; i < countCell; i++) {
//            getCell(i);
//        }
//    }
//
//    private void getCell(int index) {
//        if (row == null) {
//            return;
//        }
//        Cell cell = row.getCell(index);
//        String s = cell.toString();
////        String cellFormula = cell.getCellFormula();
////        String stringCellValue = cell.getStringCellValue();
////        String v = cell.getCTCell().get();
////        String rawValue = cell.getRawValue();
////        if (cell != null) {
////            CellType cellType = cell.getCellType();
////        }
//
////        cell.setCellType(CellType.STRING);
////        String stringCellValue = cell.getStringCellValue();
////        double numericCellValue = cell.getNumericCellValue();
////        NumberFormat numberFormat = NumberFormat.getInstance();
////        numberFormat.setGroupingUsed(false);
////        String format = numberFormat.format(Double.valueOf(stringCellValue));
////        CellType cellType = cell.getCellType();
//
//
//    }
//
//
////    public void getContent() throws Exception {
////        File file = new File("C:\\Users\\<USER>\\Downloads\\收款记录模板.xlsx");
////        InputStream inputStream = new FileInputStream(file);
////        //获取第一个工作表
////        XSSFSheet hs =
////        int first = hs.getFirstRowNum();//第一个行号
////        int last = hs.getLastRowNum();//最后一个行号
////
////        //遍历获取单元格里的信息
////        List<Map<String, Object>> list = new LinkedList<Map<String, Object>>();
////        Map<String, Object> map = null;
////        int rowNum = first+2;
////        for (int i = first+1; i <= last; i++) {
////            map = new HashMap<>();
////            XSSFRow row = hs.getRow(i);
////            XSSFCell invoiceCodingCell = row.getCell(1);
////            CellType invoiceCodingCellType = invoiceCodingCell.getCellType();
////            invoiceCodingCell.setCellType(CellType.STRING);
////            String invoiceCoding = invoiceCodingCell.getStringCellValue();
////            if (invoiceCoding == null) {
////                System.out.println("第"+rowNum+"行第2列不能为空");
////            }
////            map.put("invoiceCoding",invoiceCoding);
////            XSSFCell invoiceNumberCell = row.getCell(2);
////            invoiceNumberCell.setCellType(CellType.STRING);
////            String invoiceNumber = invoiceNumberCell.getStringCellValue();
////            if (invoiceNumber == null) {
////                System.out.println("第"+rowNum+"行第3列不能为空");
////            }
////            map.put("invoiceNumber",invoiceNumber);
////
////
////            XSSFCell receivableAmountCell = row.getCell(3);
////            CellType receivableAmountCellType = receivableAmountCell.getCellType();
////            if (receivableAmountCellType.equals(CellType.NUMERIC)) {
////                BigDecimal receivableAmount = new BigDecimal(receivableAmountCell.getNumericCellValue());
////                if (receivableAmount == null) {
////                    System.out.println("第"+rowNum+"行第4列不能为空");
////
////                }
////                map.put("receivableAmount",receivableAmount);
////            } else {
////                System.out.println("第"+rowNum+"行第4列必须是数值");
////            }
////
////            XSSFCell receivableTimeCell = row.getCell(4);
////            CellType receivableTimeCellType = receivableTimeCell.getCellType();
////            if (receivableTimeCellType.equals(CellType.NUMERIC)) {
////                Date receivableTime = receivableTimeCell.getDateCellValue();
////                if (receivableTime == null) {
////                    System.out.println("第"+rowNum+"行第5列不能为空");
////                }
////                map.put("receivableTime",receivableTime);
////            } else {
////                System.out.println("第"+rowNum+"行第5列日期格式不正确");
////            }
////
////            XSSFCell remarkCell = row.getCell(5);
////            if (remarkCell != null) {
////                CellType remarkCellType = remarkCell.getCellType();
////                String remark = remarkCell.getStringCellValue();
////                if (remark.length() > 200) {
////                    System.out.println("第"+rowNum+"行第6列内容过多");
////                }
////                map.put("remark",remark);
////            }
////
////            list.add(map);
////            rowNum++;
////        }
////        System.out.println(JSON.toJSONString(list));
////    }
//
//}
