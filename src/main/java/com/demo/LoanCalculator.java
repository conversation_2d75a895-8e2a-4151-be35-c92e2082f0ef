package com.demo;

import java.util.ArrayList;
import java.util.List;

public class LoanCalculator {

    /**
     * 计算分期还款的名义年化利率（APR）
     *
     * @param principal 本金金额
     * @param interestPerPeriod 每期利息金额（固定不变）
     * @param numberOfPeriods 分期期数
     * @return 年化利率（百分比形式，如15.6表示15.6%）
     */
    public static double calculateAnnualPercentageRate(
            double principal,
            double interestPerPeriod,
            int numberOfPeriods
    ) {
        // 1. 计算每期还款总额（本金均分 + 固定利息）
        double paymentPerPeriod = principal / numberOfPeriods + interestPerPeriod;

        // 2. 准备现金流列表（初始本金为正，后续还款为负）
        List<Double> cashFlows = new ArrayList<>();
        cashFlows.add(principal); // 初始获得本金（正值）
        for (int i = 0; i < numberOfPeriods; i++) {
            cashFlows.add(-paymentPerPeriod); // 每期还款（负值）
        }

        // 3. 使用二分法求解内部收益率（每期利率）
        double low = 0.0;        // 利率下限（0%）
        double high = 1.0;        // 利率上限（100%，实际年化利率不会超过100%）
        double precision = 1e-6;  // 计算精度（0.0001%）
        double mid = 0.0;

        // 最多迭代100次确保收敛
        for (int i = 0; i < 100; i++) {
            mid = (low + high) / 2;
            double npv = calculateNPV(cashFlows, mid);

            if (Math.abs(npv) < precision) {
                break; // 找到足够精确的解
            } else if (npv > 0) {
                low = mid; // NPV为正说明利率偏低
            } else {
                high = mid; // NPV为负说明利率偏高
            }
        }

        // 4. 将每期利率转换为年化利率（名义APR = 每期利率 × 每年期数）
        double periodRate = mid;
        double annualRate = periodRate * 12 * 100; // 按月计算，乘以12转为年利率

        // 保留两位小数（更符合金融显示习惯）
        return Math.round(annualRate * 100.0) / 100.0;
    }

    /**
     * 计算现金流的净现值（NPV）
     *
     * @param cashFlows 现金流列表（首期为正，后续为负）
     * @param rate 每期贴现率
     * @return 净现值
     */
    private static double calculateNPV(List<Double> cashFlows, double rate) {
        double npv = 0.0;
        int period = 0;

        for (double cashFlow : cashFlows) {
            // NPV = Σ [CF_t / (1 + r)^t]
            npv += cashFlow / Math.pow(1 + rate, period);
            period++;
        }

        return npv;
    }

    // 测试用例
    public static void main(String[] args) {
        // 示例：借款10,000元，分12期，每期利息60元（月费率0.6%）
        double principal = 16200;
        double monthlyInterest = 33.05;
        int periods = 18;

        double apr = calculateAnnualPercentageRate(principal, monthlyInterest, periods);
        System.out.println("名义年化利率(APR): " + apr + "%");
        double aaaa = calculateAnnualPercentageRate(10000, 60, 12);
        System.out.println("计算结果: " + aaaa + "%"); // 输出: 13.03%
        /*
        预期结果：
          每期还款 = 10,000/12 + 60 = 893.33元
          实际APR应为13.03%（Excel验证结果）
         */
    }


}
