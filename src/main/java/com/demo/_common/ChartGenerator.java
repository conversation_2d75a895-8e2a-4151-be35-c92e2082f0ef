package com.demo._common;

import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;

import java.io.File;
import java.io.IOException;
import java.util.Map;

/**
 * 图表生成工具类
 * 使用JFreeChart生成各种类型的图表
 */
public class ChartGenerator {
    
    /**
     * 生成柱状图
     */
    public static void generateBarChart(Map<String, Number> data, String title, 
                                      String outputPath, int width, int height) throws IOException {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();
        
        for (Map.Entry<String, Number> entry : data.entrySet()) {
            dataset.addValue(entry.getValue(), "数据", entry.getKey());
        }
        
        JFreeChart chart = ChartFactory.createBarChart(
            title,           // 图表标题
            "类别",          // X轴标签
            "数值",          // Y轴标签
            dataset          // 数据集
        );
        
        ChartUtils.saveChartAsPNG(new File(outputPath), chart, width, height);
    }
    
    /**
     * 生成饼图
     */
    public static void generatePieChart(Map<String, Number> data, String title, 
                                      String outputPath, int width, int height) throws IOException {
        DefaultPieDataset dataset = new DefaultPieDataset();
        
        for (Map.Entry<String, Number> entry : data.entrySet()) {
            dataset.setValue(entry.getKey(), entry.getValue());
        }
        
        JFreeChart chart = ChartFactory.createPieChart(
            title,           // 图表标题
            dataset,         // 数据集
            true,            // 显示图例
            true,            // 显示工具提示
            false            // 不生成URL
        );
        
        ChartUtils.saveChartAsPNG(new File(outputPath), chart, width, height);
    }
    
    /**
     * 生成折线图
     */
    public static void generateLineChart(Map<String, Number> data, String title, 
                                       String outputPath, int width, int height) throws IOException {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();
        
        for (Map.Entry<String, Number> entry : data.entrySet()) {
            dataset.addValue(entry.getValue(), "趋势", entry.getKey());
        }
        
        JFreeChart chart = ChartFactory.createLineChart(
            title,           // 图表标题
            "时间",          // X轴标签
            "数值",          // Y轴标签
            dataset          // 数据集
        );
        
        ChartUtils.saveChartAsPNG(new File(outputPath), chart, width, height);
    }
}