package com.demo._common;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtil {


    public static int dayDifference(String startTime, String endTime) {
        Date startDate = toDate(startTime);
        Date endDate = toDate(endTime);
        if (startDate != null && endDate != null) {
            long timeImplDifference = endDate.getTime() - startDate.getTime();
            return (int) (timeImplDifference / (1000 * 3600 * 24));
        }
        return 0;
    }

    public static Date toDate(String time) {
        Date date = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            date = sdf.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }


}
