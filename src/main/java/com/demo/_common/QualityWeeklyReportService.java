package com.demo._common;

import org.apache.poi.xwpf.usermodel.*;
import java.io.*;
import java.util.*;

/**
 * 九江石化质量管理周报生成服务
 */
public class QualityWeeklyReportService {
    
    /**
     * 生成质量管理周报
     */
    public static void generateWeeklyReport(String outputPath, Map<String, String> reportData) throws Exception {
        XWPFDocument document = new XWPFDocument();
        
        try {
            // 1. 标题
            createTitle(document, "九江石化质量管理周报");
            
            // 2. 日期范围
            createDateRange(document, reportData.getOrDefault("dateRange", "2025年7月12日～2025年7月18日"));
            
            // 3. 期号
            createIssueNumber(document, reportData.getOrDefault("issueNumber", "【第65期】"));
            
            // 4. 编制信息
            createCompileInfo(document, reportData.getOrDefault("compileDate", "2025年7月18日"));
            
            // 5. 上周质量工作
            createSection(document, "上周质量工作");
            createSubSection(document, "1.装置质量管理综述");
            createContentParagraph(document, reportData.getOrDefault("distillateOutlet", 
                "关键馏出口：2#加氢精柴硫含量多点不合格（13~14.9mg/kg）；加裂航煤烟点有3点不合格（18~20.8mm）。"));
            
            createSubSection(document, "2.质量过程管控情况");
            createDetailParagraph(document, reportData.getOrDefault("gasolineData", 
                "2.1 汽油：1#催稳汽 烯烃（27.24～28.83）%，辛烷值(90.1～90.7)，终馏点（206.0～210.0）℃，氯含量（0.7～1.1）mg/kg。"));
            createDetailParagraph(document, reportData.getOrDefault("szorbData", 
                "2.2 S_Zorb装置：稳汽硫含量（4.58～12.58）mg/kg，烯烃（21.15～24.84）%，辛烷值(88.5～89.8)，损失（0.4、0.3、0.5）。"));
            
            // 6. 产品情况
            createSection(document, "二、产品情况");
            createSubSection(document, "1.一周产品综述");
            createProductSummary(document, reportData.getOrDefault("productSummary", 
                "7月12日～7月18日主产品生产情况：92#汽油国ⅥB生产8批次、95#汽油国ⅥB生产5批次..."));
            
            // 7. 重要产品质量情况
            createSubSection(document, "重要产品质量情况");
            createTableTitle(document, "表1：92#车用汽油(ⅥB)质量统计表");
            create92GasolineTable(document);
            
            createTableTitle(document, "表2：95#车用汽油(ⅥB)质量统计表");
            
            // 8. 本周工作计划
            createSection(document, "三、本周工作计划");
            
            // 保存文档
            saveDocument(document, outputPath);
            
        } finally {
            document.close();
        }
    }
    
    /**
     * 创建标题段落
     */
    private static void createTitle(XWPFDocument document, String title) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        paragraph.setSpacingBetween(1.0);
        
        XWPFRun run = paragraph.createRun();
        run.setText(title);
        run.setFontFamily("Times New Roman");
        run.setFontSize(22);
        run.setBold(false);
    }
    
    /**
     * 创建日期范围段落
     */
    private static void createDateRange(XWPFDocument document, String dateRange) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        paragraph.setSpacingBetween(1.0);
        
        XWPFRun run = paragraph.createRun();
        run.setText(dateRange);
        run.setFontFamily("Times New Roman");
        run.setFontSize(16);
        run.setBold(false);
    }
    
    /**
     * 创建期号段落
     */
    private static void createIssueNumber(XWPFDocument document, String issueNumber) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        paragraph.setSpacingBetween(1.0);
        
        XWPFRun run = paragraph.createRun();
        run.setText(issueNumber);
        run.setFontFamily("Times New Roman");
        run.setFontSize(16);
        run.setBold(false);
    }
    
    /**
     * 创建编制信息段落
     */
    private static void createCompileInfo(XWPFDocument document, String compileDate) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBetween(1.0);
        
        XWPFRun run1 = paragraph.createRun();
        run1.setText("检验计量中心编制                    ");
        run1.setFontFamily("Times New Roman");
        run1.setFontSize(16);
        
        XWPFRun run2 = paragraph.createRun();
        run2.setText(compileDate);
        run2.setFontFamily("Times New Roman");
        run2.setFontSize(16);
        
        // 在编制信息下添加加粗实线
        createBoldLine(document);
    }
    
    /**
     * 创建加粗实线
     */
    private static void createBoldLine(XWPFDocument document) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBetween(1.0);
        
        // 设置段落边框为底部加粗实线
        paragraph.setBorderBottom(Borders.THICK);
    }
    
    /**
     * 创建章节标题
     */
    private static void createSection(XWPFDocument document, String sectionTitle) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBetween(1.0);
        
        XWPFRun run = paragraph.createRun();
        run.setText(sectionTitle);
        run.setFontFamily("黑体");
        run.setFontSize(16);
        run.setBold(false);
    }
    
    /**
     * 创建子章节标题
     */
    private static void createSubSection(XWPFDocument document, String subSectionTitle) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBetween(1.0);
        
        XWPFRun run = paragraph.createRun();
        run.setText(subSectionTitle);
        run.setFontFamily("黑体");
        run.setFontSize(16);
        run.setBold(false);
    }
    
    /**
     * 创建内容段落（关键馏出口样式）
     */
    private static void createContentParagraph(XWPFDocument document, String content) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBefore(0);
        paragraph.setSpacingAfter(0);
        paragraph.setFirstLineIndent(964); // 首行缩进
        
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setFontFamily("仿宋_GB2312");
        run.setFontSize(16);
        run.setBold(true);
    }
    
    /**
     * 创建详细段落（质量过程管控样式）
     */
    private static void createDetailParagraph(XWPFDocument document, String content) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBetween(1.0);
        paragraph.setFirstLineIndent(640); // 首行缩进
        
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setFontFamily("仿宋_GB2312");
        run.setFontSize(16);
        run.setBold(false);
    }
    
    /**
     * 创建产品综述段落
     */
    private static void createProductSummary(XWPFDocument document, String content) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBetween(1.0);
        paragraph.setFirstLineIndent(964); // 首行缩进
        
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setFontFamily("仿宋_GB2312");
        run.setFontSize(16);
        run.setBold(true);
    }
    
    /**
     * 创建表格标题
     */
    private static void createTableTitle(XWPFDocument document, String tableTitle) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        paragraph.setSpacingBetween(1.0); // 修改段落间距为1
        
        XWPFRun run = paragraph.createRun();
        run.setText(tableTitle);
        run.setFontFamily("仿宋_GB2312");
        run.setFontSize(16);
        run.setBold(false);
    }
    
    /**
     * 创建92#汽油质量统计表
     */
    private static void create92GasolineTable(XWPFDocument document) {
        // 表头数据
        String[] headers = {"采样日期", "辛烷值(R)", "终馏点,℃", "苯,%(体积分数)", 
                           "芳烃,%(体积分数)", "烯烃,%(体积分数)", "总氧,%(质量分数)"};
        
        // 数据行
        String[][] data = {
            {"2025-07-12 16:00:46", "92.2", "199.5", "0.36", "19.29", "11.08", "2.42"},
            {"2025-07-12 19:00:14", "92.3", "198.5", "0.37", "19.91", "11.14", "2.49"},
            {"2025-07-14 20:00:58", "92.2", "196.6", "0.33", "21.16", "10.8", "2.32"},
            {"2025-07-15 21:00:36", "92.2", "196.9", "0.33", "21.07", "10.2", "2.58"},
            {"2025-07-16 08:00:40", "92.2", "199.5", "0.35", "18.52", "10.98", "2.38"},
            {"2025-07-17 06:00:51", "92.2", "196.9", "0.31", "18.2", "11.02", "2.66"},
            {"2025-07-17 08:00:38", "92.3", "197.8", "0.34", "22.46", "10.14", "2.22"},
            {"2025-07-18 06:00:43", "92.3", "198", "0.35", "22.94", "10.76", "1.96"}
        };
        
        // 创建表格
        XWPFTable table = document.createTable(data.length + 1, headers.length);
        table.setWidth("100%");
        
        // 设置表头
        XWPFTableRow headerRow = table.getRow(0);
        for (int i = 0; i < headers.length; i++) {
            XWPFTableCell cell = headerRow.getCell(i);
            cell.setText(headers[i]);
            setCellStyle(cell, "仿宋_GB2312", 12, false, ParagraphAlignment.CENTER);
        }
        
        // 填充数据行
        for (int i = 0; i < data.length; i++) {
            XWPFTableRow row = table.getRow(i + 1);
            for (int j = 0; j < data[i].length; j++) {
                XWPFTableCell cell = row.getCell(j);
                cell.setText(data[i][j]);
                setCellStyle(cell, "宋体", 12, false, ParagraphAlignment.CENTER);
            }
        }
    }
    
    /**
     * 设置单元格样式
     */
    private static void setCellStyle(XWPFTableCell cell, String fontFamily, int fontSize, 
                                   boolean bold, ParagraphAlignment alignment) {
        try {
            XWPFParagraph paragraph = cell.getParagraphs().get(0);
            paragraph.setAlignment(alignment);
            
            XWPFRun run = paragraph.getRuns().isEmpty() ? paragraph.createRun() : paragraph.getRuns().get(0);
            run.setFontFamily(fontFamily);
            run.setFontSize(fontSize);
            run.setBold(bold);
            
        } catch (Exception e) {
            System.err.println("设置单元格样式时出错: " + e.getMessage());
        }
    }
    
    /**
     * 保存文档
     */
    private static void saveDocument(XWPFDocument document, String outputPath) throws IOException {
        File outputFile = new File(outputPath);
        File parentDir = outputFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            document.write(fos);
        }
    }
    
    /**
     * 使用示例
     */
    public static void main(String[] args) {
        try {
            String outputPath = "D:/output/quality_weekly_report.docx";
            
            Map<String, String> reportData = new HashMap<>();
            reportData.put("dateRange", "2025年8月20日～2025年8月21日");
            reportData.put("issueNumber", "【第65期】");
            reportData.put("compileDate", "2025年8月20日");
            reportData.put("distillateOutlet", "关键馏出口：2#加氢精柴硫含量多点不合格（13~14.9mg/kg）；加裂航煤烟点有3点不合格（18~20.8mm）。");
            
            generateWeeklyReport(outputPath, reportData);
            
            System.out.println("质量管理周报生成成功: " + outputPath);
            
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("生成报告失败: " + e.getMessage());
        }
    }
}


