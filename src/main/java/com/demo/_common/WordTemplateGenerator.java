package com.demo._common;

import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.util.Units;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;

import java.io.*;
import java.util.*;

/**
 * Word模板生成器
 */
public class WordTemplateGenerator {
    
    /**
     * 根据模板生成Word文档
     */
    public static void generateDocument(String templatePath, String outputPath, 
                                      Map<String, String> textData, 
                                      Map<String, Object> chartData) throws Exception {
        
        InputStream templateStream = null;
        XWPFDocument document = null;
        FileOutputStream fos = null;
        
        try {
            // 读取模板文件
            if (templatePath.startsWith("classpath:")) {
                String resourcePath = templatePath.substring("classpath:".length());
                templateStream = WordTemplateGenerator.class.getClassLoader()
                        .getResourceAsStream(resourcePath);
                if (templateStream == null) {
                    throw new FileNotFoundException("模板文件未找到: " + resourcePath);
                }
            } else {
                templateStream = new FileInputStream(templatePath);
            }
            
            document = new XWPFDocument(templateStream);
            
            // 先处理表格插入（在文本替换之前）
            if (chartData != null && !chartData.isEmpty()) {
                insertCharts(document, chartData);
            }
            
            // 再处理文本替换
            if (textData != null && !textData.isEmpty()) {
                replaceTextInDocument(document, textData);
            }
            
            // 确保输出目录存在
            File outputFile = new File(outputPath);
            File parentDir = outputFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 保存文档
            fos = new FileOutputStream(outputPath);
            document.write(fos);
            
        } finally {
            // 关闭资源
            if (fos != null) {
                try { fos.close(); } catch (IOException e) { /* ignore */ }
            }
            if (document != null) {
                try { document.close(); } catch (IOException e) { /* ignore */ }
            }
            if (templateStream != null) {
                try { templateStream.close(); } catch (IOException e) { /* ignore */ }
            }
        }
    }
    
    /**
     * 替换文档中的文本
     */
    private static void replaceTextInDocument(XWPFDocument document, Map<String, String> textData) {
        // 替换段落中的文本
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceTextInParagraph(paragraph, textData);
        }
        
        // 替换表格中的文本
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceTextInParagraph(paragraph, textData);
                    }
                }
            }
        }
        
        // 替换页眉页脚中的文本
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                replaceTextInParagraph(paragraph, textData);
            }
        }
        
        for (XWPFFooter footer : document.getFooterList()) {
            for (XWPFParagraph paragraph : footer.getParagraphs()) {
                replaceTextInParagraph(paragraph, textData);
            }
        }
    }
    
    /**
     * 替换段落中的占位符，保持原有样式
     */
    private static void replaceTextInParagraph(XWPFParagraph paragraph, Map<String, String> textData) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) {
            return;
        }
        
        // 合并所有run的文本来查找完整的占位符
        StringBuilder fullTextBuilder = new StringBuilder();
        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text != null) {
                fullTextBuilder.append(text);
            }
        }
        
        String fullText = fullTextBuilder.toString();
        if (fullText.isEmpty()) {
            return;
        }
        
        // 检查并执行替换
        String newText = fullText;
        boolean hasReplacement = false;
        
        for (Map.Entry<String, String> entry : textData.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            if (newText.contains(placeholder)) {
                newText = newText.replace(placeholder, entry.getValue());
                hasReplacement = true;
            }
        }
        
        if (!hasReplacement) {
            return;
        }
        
        // 保持第一个run的样式，清除其他run的文本
        for (int i = runs.size() - 1; i >= 1; i--) {
            paragraph.removeRun(i);
        }
        
        // 在第一个run中设置替换后的文本
        if (!runs.isEmpty()) {
            XWPFRun firstRun = runs.get(0);
            firstRun.setText(newText, 0); // 替换第一个文本片段
        }
    }
    
    /**
     * 插入图表 - 修改为在文本替换之前处理
     */
    private static void insertCharts(XWPFDocument document, Map<String, Object> chartData) throws Exception {
        for (Map.Entry<String, Object> entry : chartData.entrySet()) {
            String chartType = entry.getKey();
            Object data = entry.getValue();
            
            if (data instanceof Map) {
                Map<String, Object> tableData = (Map<String, Object>) data;
                
                // 检查是否有placeholder属性
                if (tableData.containsKey("placeholder")) {
                    String placeholder = (String) tableData.get("placeholder");
                    List<String> headers = (List<String>) tableData.get("headers");
                    List<List<String>> rows = (List<List<String>>) tableData.get("rows");
                    
                    // 查找并替换占位符
                    replaceTablePlaceholder(document, placeholder, headers, rows);
                }
            }
        }
    }
    
    /**
     * 查找并替换表格占位符
     */
    private static void replaceTablePlaceholder(XWPFDocument document, String placeholder, 
                                              List<String> headers, List<List<String>> rows) {
        System.out.println("正在查找占位符: " + placeholder);
        
        // 遍历所有段落
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = getFullParagraphText(paragraph);
            System.out.println("段落 " + i + " 文本: [" + text + "]");
            
            if (text != null && text.contains(placeholder)) {
                System.out.println("找到占位符在段落 " + i + ": " + text);
                
                // 清除段落内容
                clearParagraphText(paragraph);
                
                // 在段落后插入表格
                XWPFTable table = createTableAfterParagraph(document, paragraph, headers, rows);
                if (table != null) {
                    System.out.println("表格已成功插入到段落 " + i + " 后面");
                } else {
                    System.out.println("表格插入失败");
                }
                return;
            }
        }
        
        // 检查表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        String text = getFullParagraphText(paragraph);
                        if (text != null && text.contains(placeholder)) {
                            System.out.println("在表格单元格中找到占位符: " + text);
                            clearParagraphText(paragraph);
                            insertTableInCell(cell, headers, rows);
                            return;
                        }
                    }
                }
            }
        }
        
        System.out.println("未找到占位符: " + placeholder);
    }
    
    /**
     * 获取段落的完整文本
     */
    private static String getFullParagraphText(XWPFParagraph paragraph) {
        StringBuilder text = new StringBuilder();
        for (XWPFRun run : paragraph.getRuns()) {
            String runText = run.getText(0);
            if (runText != null) {
                text.append(runText);
            }
        }
        return text.toString();
    }
    
    /**
     * 在段落后创建表格
     */
    private static XWPFTable createTableAfterParagraph(XWPFDocument document, XWPFParagraph paragraph,
                                                       List<String> headers, List<List<String>> rows) {
        try {
            int totalRows = (rows != null ? rows.size() : 0) + 1;
            int cols = headers.size();
            
            // 创建表格
            XWPFTable table = document.createTable(totalRows, cols);
            
            // 设置表头
            XWPFTableRow headerRow = table.getRow(0);
            for (int i = 0; i < headers.size(); i++) {
                XWPFTableCell cell = headerRow.getCell(i);
                cell.setText(headers.get(i));
                setCellStyle(cell, true);
            }
            
            // 填充数据行
            if (rows != null) {
                for (int i = 0; i < rows.size(); i++) {
                    XWPFTableRow row = table.getRow(i + 1);
                    List<String> rowData = rows.get(i);
                    
                    for (int j = 0; j < Math.min(cols, rowData.size()); j++) {
                        XWPFTableCell cell = row.getCell(j);
                        cell.setText(rowData.get(j));
                        setCellStyle(cell, false);
                    }
                }
            }
            
            // 设置表格样式
            table.setWidth("100%");
            return table;
            
        } catch (Exception e) {
            System.err.println("创建表格时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 插入柱状图
     */
    @SuppressWarnings("unchecked")
    private static void insertBarChart(XWPFDocument document, Map<String, Number> data) throws Exception {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        
        // 创建图表
        XWPFChart chart = document.createChart(400, 300);
        
        // 这里需要使用POI的图表API来创建具体的图表
        // 由于POI的图表API比较复杂，这里提供基本框架
        // 实际使用时可能需要额外的图表库如JFreeChart
        
        run.setText("图表已插入位置");
    }
    
    /**
     * 插入图片
     */
    private static void insertImage(XWPFDocument document, String imagePath) throws Exception {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        
        FileInputStream imageStream = new FileInputStream(imagePath);
        run.addPicture(imageStream, XWPFDocument.PICTURE_TYPE_PNG, "image", 
                      Units.toEMU(400), Units.toEMU(300));
        imageStream.close();
    }
    
    /**
     * 插入简单表格
     * @param document Word文档
     * @param tableData 表格数据，每个List<String>代表一行
     */
    private static void insertTable(XWPFDocument document, List<List<String>> tableData) {
        if (tableData == null || tableData.isEmpty()) {
            return;
        }
        
        int rows = tableData.size();
        int cols = tableData.get(0).size();
        
        // 创建表格
        XWPFTable table = document.createTable(rows, cols);
        
        // 设置表格样式
        table.setWidth("100%");
        
        // 填充数据
        for (int i = 0; i < rows; i++) {
            XWPFTableRow row = table.getRow(i);
            List<String> rowData = tableData.get(i);
            
            for (int j = 0; j < cols; j++) {
                XWPFTableCell cell = row.getCell(j);
                cell.setText(rowData.get(j));
                
                // 设置单元格样式
                setCellStyle(cell, i == 0); // 第一行作为表头
            }
        }
    }
    
    /**
     * 插入带表头的表格
     * @param document Word文档
     * @param headers 表头
     * @param rows 数据行
     */
    private static void insertTableWithHeader(XWPFDocument document, 
                                            List<String> headers, 
                                            List<List<String>> rows) {
        if (headers == null || headers.isEmpty()) {
            return;
        }
        
        int totalRows = rows != null ? rows.size() + 1 : 1;
        int cols = headers.size();
        
        // 创建表格
        XWPFTable table = document.createTable(totalRows, cols);
        table.setWidth("100%");
        
        // 设置表头
        XWPFTableRow headerRow = table.getRow(0);
        for (int i = 0; i < headers.size(); i++) {
            XWPFTableCell cell = headerRow.getCell(i);
            cell.setText(headers.get(i));
            setCellStyle(cell, true); // 表头样式
        }
        
        // 填充数据行
        if (rows != null) {
            for (int i = 0; i < rows.size(); i++) {
                XWPFTableRow row = table.getRow(i + 1);
                List<String> rowData = rows.get(i);
                
                for (int j = 0; j < cols && j < rowData.size(); j++) {
                    XWPFTableCell cell = row.getCell(j);
                    cell.setText(rowData.get(j));
                    setCellStyle(cell, false); // 数据行样式
                }
            }
        }
    }
    
    /**
     * 设置单元格样式
     * @param cell 单元格
     * @param isHeader 是否为表头
     */
    private static void setCellStyle(XWPFTableCell cell, boolean isHeader) {
        try {
            // 获取单元格的段落
            XWPFParagraph paragraph = cell.getParagraphs().get(0);
            XWPFRun run = paragraph.getRuns().isEmpty() ? paragraph.createRun() : paragraph.getRuns().get(0);
            
            if (isHeader) {
                // 表头样式：加粗、居中
                run.setBold(true);
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                
                // 设置背景色（浅灰色）
                cell.setColor("F2F2F2");
            } else {
                // 数据行样式：普通文本、左对齐
                run.setBold(false);
                paragraph.setAlignment(ParagraphAlignment.LEFT);
            }
            
            // 设置字体大小
            run.setFontSize(10);
            
        } catch (Exception e) {
            System.err.println("设置单元格样式时出错: " + e.getMessage());
        }
    }
    
    /**
     * 在指定位置插入表格（根据占位符）
     */
    public static void insertTableAtPlaceholder(XWPFDocument document, 
                                               String placeholder,
                                               List<String> headers, 
                                               List<List<String>> rows) {
        System.out.println("正在查找占位符: " + placeholder);
        
        // 查找并替换段落中的占位符
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText();
            System.out.println("检查段落文本: " + text);
            
            if (text != null && text.contains(placeholder)) {
                System.out.println("找到占位符: " + placeholder + " 在段落: " + text);
                
                // 清除占位符文本
                clearParagraphText(paragraph);
                
                // 在当前段落位置插入表格
                insertTableAtPosition(document, i + 1, headers, rows);
                return;
            }
        }
        
        System.out.println("在主文档段落中未找到占位符: " + placeholder);
        
        // 如果在主文档中没找到，检查表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        String text = paragraph.getText();
                        if (text != null && text.contains(placeholder)) {
                            System.out.println("在表格中找到占位符: " + placeholder);
                            clearParagraphText(paragraph);
                            insertTableInCell(cell, headers, rows);
                            return;
                        }
                    }
                }
            }
        }
        
        System.out.println("未找到占位符: " + placeholder + "，将在文档末尾插入表格");
        // 如果没找到占位符，在文档末尾插入
        insertTableWithHeader(document, headers, rows);
    }
    
    /**
     * 在指定位置插入表格
     */
    private static void insertTableAtPosition(XWPFDocument document, int position, 
                                            List<String> headers, List<List<String>> rows) {
        if (headers == null || headers.isEmpty()) {
            return;
        }
        
        int totalRows = rows != null ? rows.size() + 1 : 1;
        int cols = headers.size();
        
        // 创建表格
        XWPFTable table = document.createTable(totalRows, cols);
        
        // 设置表头
        XWPFTableRow headerRow = table.getRow(0);
        for (int i = 0; i < headers.size(); i++) {
            XWPFTableCell cell = headerRow.getCell(i);
            cell.setText(headers.get(i));
            setCellStyle(cell, true);
        }
        
        // 填充数据行
        if (rows != null) {
            for (int i = 0; i < rows.size(); i++) {
                XWPFTableRow row = table.getRow(i + 1);
                List<String> rowData = rows.get(i);
                
                for (int j = 0; j < cols && j < rowData.size(); j++) {
                    XWPFTableCell cell = row.getCell(j);
                    cell.setText(rowData.get(j));
                    setCellStyle(cell, false);
                }
            }
        }
        
        // 设置表格样式
        table.setWidth("100%");
        System.out.println("表格已插入到位置: " + position);
    }
    
    /**
     * 在表格单元格中插入表格
     */
    private static void insertTableInCell(XWPFTableCell cell, List<String> headers, List<List<String>> rows) {
        // 在单元格中创建新的段落来放置表格内容
        XWPFParagraph paragraph = cell.addParagraph();
        XWPFRun run = paragraph.createRun();
        
        // 简单的文本表格格式
        StringBuilder tableText = new StringBuilder();
        
        // 添加表头
        for (int i = 0; i < headers.size(); i++) {
            tableText.append(headers.get(i));
            if (i < headers.size() - 1) tableText.append("\t");
        }
        tableText.append("\n");
        
        // 添加数据行
        if (rows != null) {
            for (List<String> row : rows) {
                for (int i = 0; i < row.size(); i++) {
                    tableText.append(row.get(i));
                    if (i < row.size() - 1) tableText.append("\t");
                }
                tableText.append("\n");
            }
        }
        
        run.setText(tableText.toString());
    }
    
    /**
     * 清除段落文本但保持段落结构
     */
    private static void clearParagraphText(XWPFParagraph paragraph) {
        try {
            List<XWPFRun> runs = new ArrayList<>(paragraph.getRuns());
            for (int i = runs.size() - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }
        } catch (Exception e) {
            System.err.println("清除段落文本时出错: " + e.getMessage());
        }
    }
}









