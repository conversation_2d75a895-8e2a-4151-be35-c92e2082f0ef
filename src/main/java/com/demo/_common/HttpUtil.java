package com.demo._common;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;

public  class HttpUtil {

    public static String doPost(String uri, String params) throws IOException {
        HttpPost httppost = new HttpPost(uri);
        StringEntity entity = new StringEntity(params, "utf-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httppost.setEntity(entity);
        HttpClient httpClient = new DefaultHttpClient();
        HttpResponse httpResponse = httpClient.execute(httppost);
        httppost.releaseConnection();
        String strResult = EntityUtils.toString(httpResponse.getEntity());
        return strResult;
    }

    public static String doPost(String uri, String params, Map<String, String> headers) throws IOException {
        HttpPost httppost = new HttpPost(uri);
        StringEntity entity = new StringEntity(params, "utf-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httppost.setEntity(entity);
        if (headers != null) {
            for (Map.Entry<String,String> header : headers.entrySet()) {
                httppost.setHeader(header.getKey(), header.getValue());
            }
        }
        HttpClient httpClient = new DefaultHttpClient();
        HttpResponse httpResponse = httpClient.execute(httppost);
        httppost.releaseConnection();
        String strResult = EntityUtils.toString(httpResponse.getEntity());
        return strResult;
    }

}
