package com.demo._common;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;

import java.io.InputStream;
import java.util.*;

/**
 * Word文档样式提取器
 */
public class WordStyleExtractor {

    public static void main(String[] args) {
        try {
            String templatePath = "aaa.docx";
            extractAllStyles(templatePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 提取Word模板中的所有文本样式
     */
    public static void extractAllStyles(String templatePath) throws Exception {
        InputStream templateStream = WordStyleExtractor.class.getClassLoader()
                .getResourceAsStream(templatePath);

        if (templateStream == null) {
            throw new RuntimeException("模板文件未找到: " + templatePath);
        }

        try (XWPFDocument document = new XWPFDocument(templateStream)) {
            System.out.println("=== Word文档样式分析 ===");

            // 提取段落样式
            extractParagraphStyles(document);

            // 提取表格样式
            extractTableStyles(document);

            // 提取文档级别样式
            extractDocumentStyles(document);
        }
    }

    /**
     * 提取段落样式
     */
    private static void extractParagraphStyles(XWPFDocument document) {
        System.out.println("\n--- 段落样式 ---");

        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText().trim();

            if (!text.isEmpty()) {
                System.out.println("段落 " + i + ": " + text);
                analyzeParagraphStyle(paragraph);
                System.out.println();
            }
        }
    }

    /**
     * 分析段落样式
     */
    private static void analyzeParagraphStyle(XWPFParagraph paragraph) {
        // 段落对齐方式
        ParagraphAlignment alignment = paragraph.getAlignment();
        System.out.println("  对齐方式: " + alignment);

        // 段落间距
        int spacingBefore = paragraph.getSpacingBefore();
        int spacingAfter = paragraph.getSpacingAfter();
        System.out.println("  段前间距: " + spacingBefore + ", 段后间距: " + spacingAfter);

        // 行间距
        double lineSpacing = paragraph.getSpacingBetween();
        System.out.println("  行间距: " + lineSpacing);

        // 缩进
        int firstLineIndent = paragraph.getFirstLineIndent();
        int leftIndent = paragraph.getIndentFromLeft();
        int rightIndent = paragraph.getIndentFromRight();
        System.out.println("  首行缩进: " + firstLineIndent + ", 左缩进: " + leftIndent + ", 右缩进: " + rightIndent);

        // 分析Run样式
        List<XWPFRun> runs = paragraph.getRuns();
        for (int j = 0; j < runs.size(); j++) {
            XWPFRun run = runs.get(j);
            String runText = run.getText(0);
            if (runText != null && !runText.trim().isEmpty()) {
                System.out.println("  Run " + j + ": " + runText.trim());
                analyzeRunStyle(run);
            }
        }
    }

    /**
     * 分析Run样式
     */
    private static void analyzeRunStyle(XWPFRun run) {
        // 字体信息
        String fontFamily = run.getFontFamily();
        int fontSize = run.getFontSize();
        System.out.println("    字体: " + fontFamily + ", 大小: " + fontSize);

        // 字体样式
        boolean bold = run.isBold();
        boolean italic = run.isItalic();
        UnderlinePatterns underline = run.getUnderline();
        boolean strikethrough = run.isStrikeThrough();

        System.out.println("    粗体: " + bold + ", 斜体: " + italic +
                          ", 下划线: " + underline + ", 删除线: " + strikethrough);

        // 字体颜色
        String color = run.getColor();
        System.out.println("    颜色: " + color);

        // 高亮颜色
        String highlightColor = getHighlightColor(run);
        if (highlightColor != null) {
            System.out.println("    高亮: " + highlightColor);
        }

        // 上下标
        VerticalAlign verticalAlign = run.getSubscript();
        System.out.println("    垂直对齐: " + verticalAlign);
    }

    /**
     * 提取表格样式
     */
    private static void extractTableStyles(XWPFDocument document) {
        System.out.println("\n--- 表格样式 ---");

        List<XWPFTable> tables = document.getTables();
        for (int i = 0; i < tables.size(); i++) {
            XWPFTable table = tables.get(i);
            System.out.println("表格 " + i + ":");
            analyzeTableStyle(table);
            System.out.println();
        }
    }

    /**
     * 分析表格样式
     */
    private static void analyzeTableStyle(XWPFTable table) {
        // 表格宽度
        int width = table.getWidth();
        System.out.println("  表格宽度: " + width);

        // 表格边框
        System.out.println("  边框样式: " + getTableBorderInfo(table));

        // 分析每个单元格
        List<XWPFTableRow> rows = table.getRows();
        for (int i = 0; i < rows.size(); i++) {
            XWPFTableRow row = rows.get(i);
            List<XWPFTableCell> cells = row.getTableCells();

            for (int j = 0; j < cells.size(); j++) {
                XWPFTableCell cell = cells.get(j);
                String cellText = cell.getText().trim();

                if (!cellText.isEmpty()) {
                    System.out.println("  单元格[" + i + "," + j + "]: " + cellText);
                    analyzeCellStyle(cell);
                }
            }
        }
    }

    /**
     * 分析单元格样式
     */
    private static void analyzeCellStyle(XWPFTableCell cell) {
        // 背景色
        String backgroundColor = cell.getColor();
        System.out.println("    背景色: " + backgroundColor);

        // 单元格内的段落样式
        List<XWPFParagraph> paragraphs = cell.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            if (!paragraph.getText().trim().isEmpty()) {
                System.out.println("    段落对齐: " + paragraph.getAlignment());

                for (XWPFRun run : paragraph.getRuns()) {
                    if (run.getText(0) != null && !run.getText(0).trim().isEmpty()) {
                        System.out.println("    字体: " + run.getFontFamily() +
                                          ", 大小: " + run.getFontSize() +
                                          ", 粗体: " + run.isBold());
                    }
                }
            }
        }
    }

    /**
     * 提取文档级别样式
     */
    private static void extractDocumentStyles(XWPFDocument document) {
        System.out.println("\n--- 文档样式 ---");

        // 获取样式定义
        XWPFStyles styles = document.getStyles();
        if (styles != null) {
            List<XWPFStyle> styleList = (List<XWPFStyle>) styles.getLatentStyles();
            for (XWPFStyle style : styleList) {
                System.out.println("样式ID: " + style.getStyleId());
                System.out.println("样式名称: " + style.getName());
                System.out.println("样式类型: " + style.getType());
                System.out.println();
            }
        }
    }

    /**
     * 获取高亮颜色
     */
    private static String getHighlightColor(XWPFRun run) {
        try {
            CTRPr rPr = run.getCTR().getRPr();
            if (rPr != null && rPr.getHighlight() != null) {
                return rPr.getHighlight().getVal().toString();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }

    /**
     * 获取表格边框信息
     */
    private static String getTableBorderInfo(XWPFTable table) {
        try {
            return "标准边框"; // 简化处理
        } catch (Exception e) {
            return "无法获取边框信息";
        }
    }


}