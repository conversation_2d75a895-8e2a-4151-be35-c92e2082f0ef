package com.demo._common;


import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.converts.DmTypeConvert;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

public class MybatisPlusGenerator {

    public static void main(String[] args) {
        // 数据库连接地址，
        String url = "*****************************************";
        // 数据库用户名
        String name = "qm";
        // 数据库密码
        String password = "Tst2020$Qm";


        String[] tableName = {"T_RTDBLINK_EQLIMS_JJSH_NEW"};
        String projectPath = System.getProperty("user.dir");
        //数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setDbType(DbType.ORACLE);
        dataSourceConfig.setDriverName("oracle.jdbc.OracleDriver");
        dataSourceConfig.setUrl(url);
        dataSourceConfig.setUsername(name);
        dataSourceConfig.setPassword(password);
        dataSourceConfig.setTypeConvert(new EasyDmTypeConvert());
        // 全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setAuthor("liumengnan");
        globalConfig.setFileOverride(true);
        globalConfig.setDateType(DateType.ONLY_DATE);
        globalConfig.setIdType(IdType.AUTO);
        globalConfig.setSwagger2(false);
        globalConfig.setOpen(false);
        globalConfig.setOutputDir(projectPath + "/java-demo/src/main/java");
        // 包配置
        PackageConfig packageConfig = new PackageConfig();
        packageConfig.setParent("com.pcitc.qm.mapper");
        packageConfig.setXml("mapper");
        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setController(null);
        //策略配置
        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig.setTablePrefix("T_AE_", "T_AD_", "T_PM_", "T_SA_", "T_AR_", "T_ASA_","t_ad_","t_pm_","t_pm_","T_CD_","T_BC_","T_");
        strategyConfig.setNaming(NamingStrategy.underline_to_camel);
        strategyConfig.setColumnNaming(NamingStrategy.underline_to_camel);
        strategyConfig.setInclude(tableName);
        strategyConfig.setEntityLombokModel(true);

        FreemarkerTemplateEngine templateEngine = new FreemarkerTemplateEngine();

        AutoGenerator autoGenerator = new AutoGenerator();
        autoGenerator.setDataSource(dataSourceConfig);
        autoGenerator.setGlobalConfig(globalConfig);
        autoGenerator.setPackageInfo(packageConfig);
        autoGenerator.setTemplate(templateConfig);
        autoGenerator.setStrategy(strategyConfig);
        autoGenerator.setTemplateEngine(templateEngine);
        autoGenerator.execute();

    }


}

class EasyDmTypeConvert extends DmTypeConvert {
    @Override
    public IColumnType processTypeConvert(GlobalConfig config, String fieldType) {
        IColumnType iColumnType = super.processTypeConvert(config, fieldType);
        if (fieldType.equals("BigInteger")) {
            iColumnType = DbColumnType.LONG;
        }
        if (fieldType.equals("SMALLINT")) {
            iColumnType = DbColumnType.INTEGER;
        }
        return iColumnType;
    }
}
