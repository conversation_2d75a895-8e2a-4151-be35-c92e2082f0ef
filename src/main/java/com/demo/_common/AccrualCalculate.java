package com.demo._common;

import java.math.BigDecimal;

/**
 * 欠款利息
 */
public class AccrualCalculate {

    public static BigDecimal calculate(String startTime, String endTime, String money) {
        BigDecimal rateDecimal = new BigDecimal("0.0004");
        BigDecimal moneyDecimal = new BigDecimal(money);
        BigDecimal accrual = moneyDecimal.multiply(rateDecimal);
        int i = DateUtil.dayDifference(startTime, endTime);
        return accrual.multiply(new BigDecimal(i));
    }

}
