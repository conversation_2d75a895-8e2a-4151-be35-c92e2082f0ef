//package com.demo._common;
//
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 质量管理周报生成示例
// */
//public class QualityReportExample {
//
//    public static void main(String[] args) {
//        try {
//            String outputPath = "D:/output/quality_report.docx";
//
//            // 准备文本数据
//            Map<String, String> textData = new HashMap<>();
//            textData.put("detectionTime", "2025年8月20日~2025年8月21日");
//            textData.put("number", "65");
//            textData.put("generateTime", "2025年8月20日");
//            textData.put("distillateOutlet", "2#加氢精柴硫含量多点不合格（13~14.9mg/kg）；加裂航煤烟点有3点不合格（18~20.8mm）。");
//
//            // 准备表格数据
//            Map<String, Object> tableData = new HashMap<>();
//            Map<String, Object> qualityTableData = new HashMap<>();
//            qualityTableData.put("headers", Arrays.asList(
//                "采样日期", "辛烷值(R)", "终馏点,℃", "苯,%(体积分数)",
//                "芳烃,%(体积分数)", "烯烃,%(体积分数)", "总氧,%(质量分数)"
//            ));
//            qualityTableData.put("rows", Arrays.asList(
//                Arrays.asList("2025-07-12 16:00:46", "92.2", "199.5", "0.36", "19.29", "11.08", "2.42"),
//                Arrays.asList("2025-07-12 19:00:14", "92.3", "198.5", "0.37", "19.91", "11.14", "2.49"),
//                Arrays.asList("2025-07-14 20:00:58", "92.2", "196.6", "0.33", "21.16", "10.8", "2.32"),
//                Arrays.asList("2025-07-15 21:00:36", "92.2", "196.9", "0.33", "21.07", "10.2", "2.58")
//            ));
//            tableData.put("qualityTable", qualityTableData);
//
//            // 生成文档
//            WordDocumentService.generateQualityReport(outputPath, textData, tableData);
//
//            System.out.println("质量管理周报生成成功: " + outputPath);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.err.println("生成报告失败: " + e.getMessage());
//        }
//    }
//}