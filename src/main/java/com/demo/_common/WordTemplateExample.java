package com.demo._common;

import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;
import java.util.List;

/**
 * Word模板生成器使用示例
 */
public class WordTemplateExample {
    
    public static void main(String[] args) {
        try {
            String templatePath = WordTemplateExample.class.getClassLoader().getResource("QualityManageWeekly_Template.docx").getPath();
            String outputPath = "D:/output/generated_report.docx";
            
            System.out.println("开始生成Word文档...");
            
            // 准备文本替换数据
            Map<String, String> textData = new HashMap<>();
            textData.put("detectionTime", "2025年8月20日~2025年8月21日");
            textData.put("number", "65");
            textData.put("generateTime", "2025年8月20日");
            textData.put("distillateOutlet", "2#加氢精柴硫含量多点不合格（13~14.9mg/kg）；加裂航煤烟点有3点不合格（18~20.8mm）。");
            
            // 准备图表数据 - 使用占位符定位
            Map<String, Object> chartData = new HashMap<>();
            
            // 表1：92#车用汽油(VIB)原量统计表 - 插入到${qualityTable}位置
            Map<String, Object> qualityTableData = new HashMap<>();
            qualityTableData.put("headers", Arrays.asList("采样日期", "辛烷值(R)", "终馏点,℃", "苯,%(体积分数)", "芳烃,%(体积分数)", "烯烃,%(体积分数)", "总氧,%(质量分数)"));
            qualityTableData.put("rows", Arrays.asList(
                Arrays.asList("2025-07-12 16:00:46", "92.2", "199.5", "0.36", "19.29", "11.08", "2.42"),
                Arrays.asList("2025-07-12 19:00:14", "92.3", "198.5", "0.37", "19.91", "11.14", "2.49"),
                Arrays.asList("2025-07-14 20:00:58", "92.2", "196.6", "0.33", "21.16", "10.8", "2.32"),
                Arrays.asList("2025-07-15 21:00:36", "92.2", "196.9", "0.33", "21.07", "10.2", "2.58"),
                Arrays.asList("2025-07-16 08:00:40", "92.2", "199.5", "0.35", "18.52", "10.98", "2.38"),
                Arrays.asList("2025-07-17 06:00:51", "92.2", "196.9", "0.31", "18.2", "11.02", "2.66"),
                Arrays.asList("2025-07-17 08:00:38", "92.3", "197.8", "0.34", "22.46", "10.14", "2.22"),
                Arrays.asList("2025-07-18 06:00:43", "92.3", "198", "0.35", "22.94", "10.76", "1.96")
            ));
            qualityTableData.put("placeholder", "${qualityTable}");
            chartData.put("qualityTable", qualityTableData);
            
            // 表2：95#车用汽油(VIB)原量统计表 - 插入到${table2}位置
            Map<String, Object> table2Data = new HashMap<>();
            table2Data.put("headers", Arrays.asList("采样日期", "辛烷值(R)", "终馏点,℃", "蒸气压(37.8℃),kPa", "苯,%(体积分数)", "芳烃,%(体积分数)", "烯烃,%(体积分数)", "总氧,%(质量分数)"));
            table2Data.put("rows", Arrays.asList(
                Arrays.asList("2025-07-12 06:00:31", "95.1", "198.6", "61.3", "0.31", "21.96", "11.42", "2.14"),
                Arrays.asList("2025-07-12 19:00:55", "95.6", "198.5", "62", "0.31", "21.05", "11.26", "2.54"),
                Arrays.asList("2025-07-13 16:00:00", "95.7", "200.1", "62.1", "0.32", "21.56", "11.86", "2.43"),
                Arrays.asList("2025-07-15 08:00:31", "95.6", "198.1", "61.2", "0.31", "20.86", "11.68", "2.51")
            ));
            table2Data.put("placeholder", "${table2}");
            chartData.put("table2", table2Data);
            
            // 生成包含表格的文档
            WordTemplateGenerator.generateDocument(templatePath, outputPath, textData, chartData);
            
            System.out.println("Word文档生成成功: " + outputPath);
            System.out.println("请检查控制台输出，确认占位符是否被找到");
            
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("生成Word文档失败: " + e.getMessage());
        }
    }
}







