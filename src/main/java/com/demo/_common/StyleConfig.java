package com.demo._common;

import java.util.HashMap;
import java.util.Map;

/**
 * 样式配置类
 */
public class StyleConfig {
    
    public static final Map<String, Map<String, Object>> PARAGRAPH_STYLES = new HashMap<>();
    public static final Map<String, Map<String, Object>> TABLE_STYLES = new HashMap<>();
    
    static {
        // 标题样式
        Map<String, Object> titleStyle = new HashMap<>();
        titleStyle.put("fontFamily", "宋体");
        titleStyle.put("fontSize", 16);
        titleStyle.put("bold", true);
        titleStyle.put("alignment", "CENTER");
        PARAGRAPH_STYLES.put("title", titleStyle);
        
        // 正文样式
        Map<String, Object> normalStyle = new HashMap<>();
        normalStyle.put("fontFamily", "宋体");
        normalStyle.put("fontSize", 12);
        normalStyle.put("bold", false);
        normalStyle.put("alignment", "LEFT");
        PARAGRAPH_STYLES.put("normal", normalStyle);
        
        // 表头样式
        Map<String, Object> headerStyle = new HashMap<>();
        headerStyle.put("backgroundColor", "F2F2F2");
        headerStyle.put("bold", true);
        headerStyle.put("alignment", "CENTER");
        TABLE_STYLES.put("header", headerStyle);
    }
}