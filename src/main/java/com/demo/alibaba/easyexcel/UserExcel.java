package com.demo.alibaba.easyexcel;

import com.alibaba.excel.annotation.ExcelProperty;
public class UserExcel {

//    @ExcelProperty("序号")
    private String num;


    /**
     * 姓名
     */
//    @ExcelProperty("姓名")
    private String userName;

    /**
     * 手机号码
     */
//    @ExcelProperty("手机号码")
    private String mobile;

    /**
     * 石化通（统一身份账号）
     */
//    @ExcelProperty("统一身份账号")
    private String userCode;

    /**
     * 石化邮箱
     */
//    @ExcelProperty("中国石化邮箱")
    private String email;
    /**
     * 群组id
     */
//    @ExcelProperty("群组ID")
    private String groupId;
    /**
     * 群组id
     */
//    @ExcelProperty("群组")
    private String groupName;

    /**
     * 装置ID
     */
//    @ExcelProperty("装置编码")
    private String unitId;
    /**
     * 装置ID
     */
//    @ExcelProperty("装置")
    private String unitName;

    /**
     * 车间ID
     */
//    @ExcelProperty("车间编码")
    private String workshopId;
    /**
     * 车间ID
     */
//    @ExcelProperty("车间")
    private String workshopName;

    /**
     * 工厂ID
     */
//    @ExcelProperty("工厂编码")
    private String factoryId;

    /**
     * 工厂ID
     */
//    @ExcelProperty("工厂")
    private String factoryName;

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getWorkshopId() {
        return workshopId;
    }

    public void setWorkshopId(String workshopId) {
        this.workshopId = workshopId;
    }

    public String getWorkshopName() {
        return workshopName;
    }

    public void setWorkshopName(String workshopName) {
        this.workshopName = workshopName;
    }

    public String getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }
}
