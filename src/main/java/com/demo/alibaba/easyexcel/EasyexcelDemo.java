package com.demo.alibaba.easyexcel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EasyexcelDemo {

    public static void main(String[] args) {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE1.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);//字体大小
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        // 背景绿色
//        contentWriteCellStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
//        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
//        contentWriteFont.setFontHeightInPoints((short)20);
//        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

        // 头的策略
        WriteCellStyle headWriteCellStyle2 = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle2.setFillForegroundColor(IndexedColors.RED1.getIndex());
        WriteFont headWriteFont2 = new WriteFont();
        headWriteFont2.setFontHeightInPoints((short) 17);//字体大小
        headWriteCellStyle2.setWriteFont(headWriteFont2);

        HorizontalCellStyleStrategy horizontalCellStyleStrategy2 =
                new HorizontalCellStyleStrategy(headWriteCellStyle2, contentWriteCellStyle);

        String fileName = "D:/" + "simpleWrite" + System.currentTimeMillis() + ".xlsx";
        ExcelWriter excelWriter = EasyExcel.write(fileName, DataDemo.class)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .registerWriteHandler(horizontalCellStyleStrategy2) .build();
        for (int i = 0; i < 2; i++) {
            WriteSheet writeSheet = EasyExcel.writerSheet(i).build();
            excelWriter.write(data(), writeSheet);
        }
        if (excelWriter != null) {
            excelWriter.finish();
        }
    }

    private static List<DataDemo> data() {
        List<DataDemo> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            DataDemo data = new DataDemo();
            data.setString("字符串" + i);
            data.setDate(new Date());
            data.setDoubleData(new BigDecimal("0.56"));
            data.setStringA("a"+i);
            data.setStringB("b"+i);
            data.setStringC("c"+i);
            data.setStringD("d"+i);
            data.setStringE("e"+i);
            data.setStringF("f"+i);
            list.add(data);
        }
        return list;
    }
}
