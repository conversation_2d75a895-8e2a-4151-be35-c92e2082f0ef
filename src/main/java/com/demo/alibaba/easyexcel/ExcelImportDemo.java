package com.demo.alibaba.easyexcel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;

public class ExcelImportDemo {


    public static void main(String[] args) {

//        EasyExcel.read("D:/A.xlsx", UserExcel.class, new PageReadListener<UserExcel>(dataList -> {
//            for (DemoData demoData : dataList) {
//                log.info("读取到一条数据{}", JSON.toJSONString(demoData));
//            }
//        })).sheet().doRead();
////        EasyExcel.read(, UserExcel.class, new UserExcelReadListener()).sheet().doRead();
    }

}
