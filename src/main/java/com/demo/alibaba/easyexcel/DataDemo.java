package com.demo.alibaba.easyexcel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

public class DataDemo {

    @ExcelProperty({"主标题", "字符串标题"})
    private String string;
    @ExcelProperty({"主标题", "日期标题"})
    private Date date;
    @ExcelProperty({"主标题", "数字标题"})
    private BigDecimal doubleData;
    @ExcelProperty({"主标题", "字符串标题A"})
    private String stringA;
    @ExcelProperty({"主标题", "字符串标题B"})
    private String stringB;
    @ExcelProperty({"主标题", "字符串标题C"})
    private String stringC;
    @ExcelProperty({"主标题", "字符串标题D"})
    private String stringD;
    @ExcelProperty({"主标题", "字符串标题E"})
    private String stringE;
    @ExcelProperty({"主标题", "字符串标题F"})
    private String stringF;
    /**
     * 忽略这个字段
     */
    @ExcelIgnore
    private String ignore;

    public String getString() {
        return string;
    }

    public void setString(String string) {
        this.string = string;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getDoubleData() {
        return doubleData;
    }

    public void setDoubleData(BigDecimal doubleData) {
        this.doubleData = doubleData;
    }

    public String getStringA() {
        return stringA;
    }

    public void setStringA(String stringA) {
        this.stringA = stringA;
    }

    public String getStringB() {
        return stringB;
    }

    public void setStringB(String stringB) {
        this.stringB = stringB;
    }

    public String getStringC() {
        return stringC;
    }

    public void setStringC(String stringC) {
        this.stringC = stringC;
    }

    public String getStringD() {
        return stringD;
    }

    public void setStringD(String stringD) {
        this.stringD = stringD;
    }

    public String getStringE() {
        return stringE;
    }

    public void setStringE(String stringE) {
        this.stringE = stringE;
    }

    public String getStringF() {
        return stringF;
    }

    public void setStringF(String stringF) {
        this.stringF = stringF;
    }

    public String getIgnore() {
        return ignore;
    }

    public void setIgnore(String ignore) {
        this.ignore = ignore;
    }
}