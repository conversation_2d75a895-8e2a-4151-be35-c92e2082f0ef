package com.demo.alibaba.easyexcel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;

import java.util.Map;

public class UserExcelReadListener implements ReadListener<UserExcel> {


    @Override
    public void onException(Exception e, AnalysisContext analysisContext) throws Exception {

    }

    @Override
    public void invoke(UserExcel userExcel, AnalysisContext analysisContext) {
        String email = userExcel.getEmail();
        System.out.println(email);
    }



    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        System.out.println("所有记录已读取完成！");
    }

    @Override
    public boolean hasNext(AnalysisContext analysisContext) {
        return false;
    }

    @Override
    public void invokeHead(Map map, AnalysisContext analysisContext) {
        System.out.println("");
    }
}
