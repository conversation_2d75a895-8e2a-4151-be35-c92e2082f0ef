package com.demo.alibaba.json;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public class FastjsonDemo {

    public static void main(String[] args) {
        String json = "{\"success\":true,\"resultCode\":\"0010\",\"resultMessage\":\"返回数据为空\",\"result\":null}";
        JSONObject jsonObject = JSONObject.parseObject(json);
        List result1 = (List)jsonObject.get("result");
        String result = jsonObject.getString("result");
        System.out.println(result1 == null);
    }

}
