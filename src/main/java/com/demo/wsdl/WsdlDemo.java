package com.demo.wsdl;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import java.net.MalformedURLException;
import java.net.URL;

public class WsdlDemo {

    public static void main(String[] args) throws MalformedURLException {
        // 创建WSDL文件的URL对象
        URL wsdlUrl = new URL("http://em.sinopec.com/emwebservice/InterLockNewService?wsdl");

        // 创建服务名称的QName对象
        QName serviceName = new QName("http://pcitc.com/zhlafiles/webservice/", "InterLockNewService");

        // 创建Service对象
        Service service = Service.create(wsdlUrl, serviceName);

        // 获取端口对象
        InterLockNewService port = service.getPort(InterLockNewService.class);

        // 调用服务方法
        String result = port.queryInterLockNew(null);
        System.out.println(result);
    }
}
