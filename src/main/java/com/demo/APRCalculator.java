package com.demo;

import java.util.Arrays;

public class APRCalculator {

    /**
     * 计算分期还款的实际年化利率(有效年利率)
     *
     * @param principal 本金金额
     * @param interestPerPeriod 每期利息金额
     * @param numberOfPeriods 分期期数
     * @return 年化利率(百分比形式，如15.6表示15.6%)
     */
    public static double calculateEffectiveAPR(
            double principal,
            double interestPerPeriod,
            int numberOfPeriods
    ) {
        // 1. 计算每期还款总额(本金均分 + 固定利息)
        double principalPerPeriod = principal / numberOfPeriods;
        double paymentPerPeriod = principalPerPeriod + interestPerPeriod;

        // 2. 使用牛顿迭代法求解月利率
        double guess = 0.01; // 初始猜测值1%
        double rate = newtonRaphson(
                principal,
                paymentPerPeriod,
                numberOfPeriods,
                guess
        );

        // 3. 将月利率转换为有效年利率(EAR)
        double effectiveAnnualRate = Math.pow(1 + rate, 12) - 1;

        // 4. 转换为百分比并四舍五入保留两位小数
        return Math.round(effectiveAnnualRate * 10000) / 100.0;
    }

    /**
     * 牛顿迭代法求解月利率
     *
     * @param principal 本金
     * @param payment 每期还款额
     * @param n 期数
     * @param initialGuess 初始猜测值
     * @return 月利率
     */
    private static double newtonRaphson(
            double principal,
            double payment,
            int n,
            double initialGuess
    ) {
        double x = initialGuess;
        final double tolerance = 1e-8;
        final int maxIterations = 100;

        for (int i = 0; i < maxIterations; i++) {
            // 计算函数值 f(x) = PV - ∑[PMT/(1+x)^k]
            double f = principal;
            double df = 0; // f'(x)的导数

            for (int k = 1; k <= n; k++) {
                double factor = 1 / Math.pow(1 + x, k);
                f -= payment * factor;
                df += k * payment * factor / (1 + x);
            }

            // 检查是否达到精度要求
            if (Math.abs(f) < tolerance) {
                break;
            }

            // 牛顿迭代: x_{n+1} = x_n - f(x_n)/f'(x_n)
            x = x - f / df;
        }

        return x;
    }

    // 测试验证
    public static void main(String[] args) {
        // 测试案例1: 本金10000元，12期，每期利息60元 (预期结果约13.03%)
        double apr1 = calculateEffectiveAPR(10000, 60, 12);
        System.out.println("测试案例1 - 年化利率: " + apr1 + "%");

        // 测试案例2: 本金5000元，6期，每期利息25元
        double apr2 = calculateEffectiveAPR(5000, 25, 6);
        System.out.println("测试案例2 - 年化利率: " + apr2 + "%");

        // 测试案例3: 本金20000元，24期，每期利息100元
        double apr3 = calculateEffectiveAPR(16200, 33.04, 18);
        System.out.println("测试案例3 - 年化利率: " + apr3 + "%");

        // 测试案例4: 与Excel XIRR函数对比验证
        double apr4 = calculateEffectiveAPR(10000, 83.33, 12); // 总利息1000元
        System.out.println("测试案例4 - 年化利率: " + apr4 + "%");
    }
}
