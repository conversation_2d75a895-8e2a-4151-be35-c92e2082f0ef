package com.demo.aliyun.opensearch;

import java.nio.charset.Charset;
import java.util.Map;
import java.util.Random;

import com.aliyun.opensearch.DocumentClient;
import com.aliyun.opensearch.OpenSearchClient;
import com.aliyun.opensearch.SearcherClient;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Maps;
import com.aliyun.opensearch.sdk.dependencies.org.json.JSONArray;
import com.aliyun.opensearch.sdk.dependencies.org.json.JSONObject;
import com.aliyun.opensearch.sdk.generated.OpenSearch;
import com.aliyun.opensearch.sdk.generated.commons.OpenSearchClientException;
import com.aliyun.opensearch.sdk.generated.commons.OpenSearchException;
import com.aliyun.opensearch.sdk.generated.commons.OpenSearchResult;
import com.aliyun.opensearch.sdk.generated.document.Command;
import com.aliyun.opensearch.sdk.generated.document.DocumentConstants;
import com.aliyun.opensearch.sdk.generated.search.Config;
import com.aliyun.opensearch.sdk.generated.search.SearchFormat;
import com.aliyun.opensearch.sdk.generated.search.SearchParams;
import com.aliyun.opensearch.sdk.generated.search.general.SearchResult;

public class PushDemo {

    private static final String appName = "cc_sku_dev";
    private static final String accesskey = "LTAIBu1aAKMTo99N";
    private static final String secret = "6RzFDcNgHb0PZpux0mjSDz8Rx9twye";
    private static final String host = "http://opensearch-cn-beijing.aliyuncs.com";

    private static final String tableName = "cc_main_sku_dev";

    public static void main(String[] args) {
        //查看文件和默认编码格式
        System.out.println(String.format("file.encoding: %s", System.getProperty("file.encoding")));
        System.out.println(String.format("defaultCharset: %s", Charset.defaultCharset().name()));
        //-------------数据推送示例代码-----------------
        //生成随机数，作为主键值
        Random rand = new Random();
        int value1 = rand.nextInt(Integer.MAX_VALUE);
        //定义Map对象存储上传文档数据,此为文档1
        Map<String, Object> doc1 = Maps.newLinkedHashMap();
        doc1.put("supplier_id", 1);//ADD进行全字段更新；高级版支持update,部分字段更新。
        JSONObject json1 = new JSONObject();
        json1.put(DocumentConstants.DOC_KEY_CMD, Command.DELETE);
        json1.put(DocumentConstants.DOC_KEY_FIELDS, doc1);

        JSONArray docsJsonArr = new JSONArray();
            docsJsonArr.put(json1);//新增文档1
        String docsJson = docsJsonArr.toString();
        //创建并构造OpenSearch对象
        OpenSearch openSearch = new OpenSearch(accesskey, secret, host);
        //创建OpenSearchClient对象，并以OpenSearch对象作为构造参数
        OpenSearchClient serviceClient = new OpenSearchClient(openSearch);
        //定义DocumentClient对象添加json格式doc数据批量提交
        DocumentClient documentClient = new DocumentClient(serviceClient);
        try {
            //执行推送操作
            OpenSearchResult osr = documentClient.push(docsJson, appName, tableName);
            //判断数据是否推送成功，主要通过判断2处，第一处判断用户方推送是否成功，第二处是应用控制台中有无报错日志
            //用户方推送成功后，也有可能在应用端执行失败，此错误会直接在应用控制台错误日志中生成，比如字段内容转换失败
            if (osr.getResult().equalsIgnoreCase("true")) {
                System.out.println("用户方推送无报错！\n以下为getTraceInfo推送请求Id:" + osr.getTraceInfo().getRequestId());
            } else {
                System.out.println("用户方推送报错！" + osr.getTraceInfo());
            }
        } catch (OpenSearchException e) {
            e.printStackTrace();
        } catch (OpenSearchClientException e) {
            e.printStackTrace();
        }
    }

}
