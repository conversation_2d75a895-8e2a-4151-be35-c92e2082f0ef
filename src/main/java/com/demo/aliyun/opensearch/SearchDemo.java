package com.demo.aliyun.opensearch;

import com.aliyun.opensearch.OpenSearchClient;
import com.aliyun.opensearch.SearcherClient;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.aliyun.opensearch.sdk.generated.OpenSearch;
import com.aliyun.opensearch.sdk.generated.commons.OpenSearchClientException;
import com.aliyun.opensearch.sdk.generated.commons.OpenSearchException;
import com.aliyun.opensearch.sdk.generated.search.*;
import com.aliyun.opensearch.sdk.generated.search.general.SearchResult;
import com.aliyun.opensearch.search.SearchParamsBuilder;

public class SearchDemo {

    private static final String appName = "cc_sku_dev";
    private static final  String accesskey = "LTAIBu1aAKMTo99N";
    private static final  String secret = "6RzFDcNgHb0PZpux0mjSDz8Rx9twye";
    private static final  String host = "http://opensearch-cn-beijing.aliyuncs.com";

    public static void main(String[] args) throws OpenSearchClientException, OpenSearchException {
        //创建并构造OpenSearch对象
        OpenSearch openSearch = new OpenSearch(accesskey, secret, host);

        //创建OpenSearchClient对象，并以OpenSearch对象作为构造参数
        OpenSearchClient serviceClient = new OpenSearchClient(openSearch);

        //创建SearcherClient对象，并以OpenSearchClient对象作为构造参数
        SearcherClient searcherClient = new SearcherClient(serviceClient);

        //定义Config对象，用于设定config子句参数，分页或数据返回格式，指定应用名等等
        Config config = new Config(Lists.newArrayList(appName));
        config.setStart(0);
        config.setHits(5000);
        //设置返回格式为FULLJSON，目前支持返回 XML，JSON，FULLJSON 等格式
        config.setSearchFormat(SearchFormat.JSON);
        config.setKvpairs("business_id:127=1:15326546=2");
        // 设置搜索结果返回应用中哪些字段
        config.setFetchFields(Lists.newArrayList("sku_name","shop_id","business_id"));
        // 注意：config子句中的rerank_size参数，在Rank类对象中设置

        // 创建参数对象
        SearchParams searchParams = new SearchParams(config);

        // 设置查询子句，若需多个索引组合查询，需要setQuery处合并，否则若设置多个setQuery后面的会替换前面查询
        searchParams.setQuery("is_delete:'0'");
        SortField sortField = new SortField();
        sortField.setField("shop_id");
        sortField.setOrder(Order.DECREASE);
        Sort sort = new Sort();
        sort.addToSortFields(sortField);
        searchParams.setSort(sort);
        Rank rank = new Rank();
        rank.setSecondRankName("querybar");
        rank.setReRankSize(2000);// 设置参与精排文档个数
        searchParams.setRank(rank);
        //设置统计子句
//        Aggregate agg = new Aggregate();
//        agg.setGroupKey("brand"); //设置group_key

//        agg.setAggFun("count()");
//        //添加Aggregate对象参数
//        searchParams.addToAggregates(agg);

        // 设置查询过滤条件
//        searchParams.setFilter("shop_id!=\"200002@201023@1\""); //此处也可改用后面的ParamsBuilder实现添加过滤条件

        SearchParamsBuilder paramsBuilder = SearchParamsBuilder.create(searchParams);
        System.out.println(paramsBuilder.toString());
        SearchResult searchResult = searcherClient.execute(paramsBuilder);

        String result = searchResult.getResult();

        System.out.println(result);










    }

}