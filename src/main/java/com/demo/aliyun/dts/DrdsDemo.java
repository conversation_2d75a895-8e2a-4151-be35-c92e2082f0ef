package com.demo.aliyun.dts;

/**
 * DRDS dts订阅DEMO
 */

import java.io.UnsupportedEncodingException;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.drc.client.message.DataMessage;
import com.aliyun.drc.clusterclient.ClusterClient;
import com.aliyun.drc.clusterclient.ClusterListener;
import com.aliyun.drc.clusterclient.DefaultClusterClient;
import com.aliyun.drc.clusterclient.RegionContext;
import com.aliyun.drc.clusterclient.message.ClusterMessage;

public class DrdsDemo {

	//订阅Client
	private final ClusterClient client;

	public DrdsDemo(String accessKey, String accessSecret, String subscribeInstanceID) throws Exception {
		this.client = initClusterClient(accessKey, accessSecret, subscribeInstanceID);
	}

	private ClusterClient initClusterClient(String accessKey, String accessSecret, String subscribeInstanceID)
			throws Exception {
		// 创建一个context,仅是属性设置
		RegionContext context = new RegionContext();
		// 运行SDK的服务器是否使用公网IP连接DTS(若使用内网IP访问，需要设置false)
		// 在订阅任务启动时，需要网络连接时需要关注该设置项
		context.setUsePublicIp(true);
		// 设置用户accessKey secret
		context.setAccessKey(accessKey);
		context.setSecret(accessSecret);

		// 创建消费者
		ClusterClient client = new DefaultClusterClient(context);
		ClusterListener listener = new ClusterListener() {
			// @Override
			public void noException(Exception e) {
				// TODO Auto-generated method stub
				e.printStackTrace();
			}

			// @Override
			public void notify(List<ClusterMessage> messages) throws Exception {
				//处理订阅任务收到的消息
				for (ClusterMessage message : messages) {
					replicateMessage(message);
				}
			}
		};

		client.askForGUID(subscribeInstanceID);
		client.addConcurrentListener(listener);

		return client;
	}

	private void replicateMessage(final ClusterMessage message) {
		// 处理消息
		try {
			// 转换消息格式并处理
			convertRecord(message);
			// 确认消息以消费
			message.ackAsConsumed();
		} catch (Exception e) {
			// TODO 根据业务需求进行必要的重试
			e.printStackTrace();
		}
	}

	private void convertRecord(ClusterMessage message) throws UnsupportedEncodingException {
		DataMessage.Record record = message.getRecord();
		System.out.println("Record Op type:" + record.getOpt().toString());
		JSONObject jsonRecord;
		String key = null;
		switch (record.getOpt()) {
		case INSERT: // 数据插入
			jsonRecord = convertFields(record, 0, 1);
			key = record.getPrimaryKeys();
			System.out.println("Record Insert:Json format:" + jsonRecord.toJSONString());
			break;
		case UPDATE:// 数据更新
		case REPLACE:// replace操作
			JSONObject oldJsonRecord = convertFields(record, 0, 2);
			System.out.println("Record Update Before:Json format:" + oldJsonRecord.toJSONString());
			jsonRecord = convertFields(record, 1, 2);
			System.out.println("Record Update Before:Json format:" + jsonRecord.toJSONString());
			key = record.getPrimaryKeys();
			break;
		case DELETE:// 数据删除
			jsonRecord = convertFields(record, 0, 1);
			System.out.println("Record Delete:Json format:" + jsonRecord.toJSONString());
			key = record.getPrimaryKeys();
			break;
		default:
			return;
		}
		//数据表中对主Key列名
		System.out.println("PrimaryKey Column Name:" + key);
		//drds中物理数据库名和物理数据表名
		System.out.println("Record DB Name:"+record.getDbname()+",Table Name:"+record.getTablename());
		//drds中逻辑数据库名和逻辑表名
		System.out.println("Record Logical DB Name:"+record.getLogicalDbname()+",Table Name:"+record.getLogicalTablename());

	}

	// 将消息组成JSON格式输出
	private JSONObject convertFields(DataMessage.Record record, int start, int step)
			throws UnsupportedEncodingException {
		List<DataMessage.Record.Field> fields = record.getFieldList();
		JSONObject ret = new JSONObject();
		for (int i = start; i < fields.size(); i += step) {
			DataMessage.Record.Field field = fields.get(i);
			JSONObject object = new JSONObject();
			object.put("type", field.getType().toString());
			object.put("encoding", field.getEncoding());
			if (field.getValue() != null) {
				object.put("value", field.getValue().toString(field.getEncoding()));
			} else {
				object.put("value", null);
			}
			ret.put(field.getFieldname(), object);
		}
		return ret;
	}

	public void start() throws Exception {
		System.out.println("Start DTS subscription client...");
		client.start();
	}

	public void stop() throws Exception {
		System.out.println("Stop DTS Subscription Client...");
		client.stop();
	}

	public static void main(String[] args) {
		// 按需修改源端DTS订阅SDK的配置
		String accessKey = "xxxxx";
		String accessSecret = "xxxx";
		String subscribeInstanceID = "xxxx";
		System.out.println("accessKey:" + accessKey + ",accessSecret:" + accessSecret + ",subscribeInstanceID:"
				+ subscribeInstanceID);
		try {
			DrdsDemo dts4drds = new DrdsDemo(accessKey, accessSecret, subscribeInstanceID);
			dts4drds.start();
			dts4drds.stop();
		} catch (Exception e) {
			System.out.println("Exception:" + e.getCause().getClass() + "," + e.getCause().getMessage());
		}
	}

}
