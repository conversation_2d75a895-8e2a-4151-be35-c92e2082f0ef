package com.demo.rabbitmq;

import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import org.junit.Test;

import java.io.IOException;

/**
 * 通讯方式 Hello World!
 * 为了入门操作，使用默认交换机，一个队列被一个消费者订阅
 * <AUTHOR>
 * @date 2022-09-16  16:53
 */
public class Client_1 {

    private static final String QUEUE_NAME = "hello";

    @Test
    public void publisher() throws Exception {
        //1. 获取连接对象
        Connection connection = RBConnectionUtil.getConnection();

        //2. 创建信道
        Channel channel = connection.createChannel();

        //3. 构建队列  参数：队列名 是否持久化 是否排外（只允许一个消费者） 长时间未使用是否自动删除 其他参数
        channel.queueDeclare(QUEUE_NAME, false, false, false, null);

        //4. 发送消息
        String message = "hello rabbit";
        //参数： 交换机（默认为""） routing-Key（默认交换机就是队列名） 消息其他参数  消息
        channel.basicPublish("", QUEUE_NAME, null, message.getBytes());
    }

    @Test
    public void consumer() throws Exception {
        //1. 获取连接对象
        Connection connection = RBConnectionUtil.getConnection();

        //2. 构建信道
        Channel channel = connection.createChannel();

        //3. 构建队列  参数：队列名 是否持久化 是否排外（只允许一个消费者） 长时间未使用是否自动删除 其他参数
        channel.queueDeclare(QUEUE_NAME, false, false, false, null);

        //4. 监听消息
        DefaultConsumer callback = new DefaultConsumer(channel) {
            @Override
            public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties, byte[] body)
                    throws IOException {
                System.out.println("消费者获取到消息：" + new String(body, "UTF-8"));
            }
        };
        channel.basicConsume(QUEUE_NAME, true, callback);

        System.in.read();
    }
}
