package com.demo.aop;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022-07-28  09:53
 */
@Aspect
@Component
public class LogAspect {

//    @Around("execution(* com.demo.service.impl.*.*(..))")
//    public void around(JoinPoint joinPoint) {
//        System.out.println("环绕通知");
//    }

    @Before("execution(* com.demo.service.impl.*.*(..))")
    public void before(JoinPoint joinPoint) {
        System.out.println("前置通知");
    }

    @After("execution(* com.demo.service.impl.*.*(..))")
    public void after(JoinPoint joinPoint) {
        System.out.println("后置通知");
    }

    @AfterReturning("execution(* com.demo.service.impl.*.*(..))")
    public void afterReturn(JoinPoint joinPoint) {
        System.out.println("后置结果通知");
    }

    @AfterThrowing("execution(* com.demo.service.impl.*.*(..))")
    public void afterThrowing(JoinPoint joinPoint) {
        System.out.println("后置异常通知");
    }

}
