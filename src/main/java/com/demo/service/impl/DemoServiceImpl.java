package com.demo.service.impl;

import com.demo.dao.DemoMapper;
import com.demo.po.DemoPO;
import com.demo.service.DemoService;
import com.demo.vo.DemoResponesVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DemoServiceImpl implements DemoService {

//    @Autowired
//    private DemoMapper demoMapper;

    @Override
    public DemoResponesVO getByKey(Long id) {
//        DemoPO demoPO = demoMapper.selectById(id);
        DemoResponesVO demoResponesVO = new DemoResponesVO();
//        BeanUtils.copyProperties(demoPO,demoResponesVO);
        return demoResponesVO;
    }

}
