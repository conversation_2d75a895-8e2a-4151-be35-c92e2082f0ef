package com.demo.kafka;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.util.Properties;

public class DemoProducer {

    public static void main(String[] args) throws InterruptedException {
        Properties prop = new Properties();

        prop.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "127.0.0.1:9092");
        prop.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        prop.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        prop.put(ProducerConfig.ACKS_CONFIG, "all");
        prop.put(ProducerConfig.RETRIES_CONFIG, 0);
        prop.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        prop.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        prop.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);

        String topic = "alarm";

        KafkaProducer<String, String> producer = new KafkaProducer<>(prop);
        producer.send(new ProducerRecord<String, String>(topic, Integer.toString(1), "[{\n" +
                "    \"tagtype\": \"FB\",\n" +
                "    \"areaaddr\": \"1\",\n" +
                "    \"csaddr\": \"6\",\n" +
                "    \"prjname\": \"全局OPC\",\n" +
                "    \"groupname\": \"1\",\n" +
                "    \"reginname\": \"5\",\n" +
                "    \"aof\": \"False\",\n" +
                "    \"suppression\": \"False\",\n" +
                "    \"unit\": \"1111\",\n" +
                "    \"eventId\": \"111\",\n" +
                "    \"write_time\": \"2024-10-24 14:12:12\",\n" +
                "    \"opc_code\": \"11111\",\n" +
                "    \"dcs_code\": \"1401\",\n" +
                "    \"message\": \"NOX(干基)\",\n" +
                "    \"activetime\": \"2024-10-24 14:11:04\",\n" +
                "    \"changemask\": \"211\",\n" +
                "    \"conditionName\": \"H\",\n" +
                "    \"eventcategory\": \"Process Alarm\",\n" +
                "    \"eventtype\": \"Condition\",\n" +
                "    \"newstate\": \"3\",\n" +
                "    \"severity\": \"30\",\n" +
                "    \"source\": \"OA9328/控制域1/3号裂解炉/1101_NOX_03991A1\",\n" +
                "    \"subconditionname\": \"H\",\n" +
                "    \"fttime\": \"2024-10-24 14:11:04\"\n" +
                "}]"));
        
        producer.close();
    }

}
