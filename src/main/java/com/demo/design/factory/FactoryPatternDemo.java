package com.demo.design.factory;

/**
 * 工厂模式
 */
public class FactoryPatternDemo {

    public static void main(String[] args) {
        //实例化形状工厂
        ShapeFactory shapeFactory = new ShapeFactory();

        //获取 Circle 圆形 的对象，并调用它的 draw 方法
        Shape circle = shapeFactory.getShape("CIRCLE");
        //调用 Circle 的 draw 方法
        circle.draw();

        //获取 Rectangle 长方形 的对象，并调用它的 draw 方法
        Shape rectangle = shapeFactory.getShape("RECTANGLE");
        //调用 Rectangle 的 draw 方法
        rectangle.draw();

        //获取 Square 正方形 的对象，并调用它的 draw 方法
        Shape square = shapeFactory.getShape("SQUARE");
        //调用 Square 的 draw 方法
        square.draw();

    }

}
