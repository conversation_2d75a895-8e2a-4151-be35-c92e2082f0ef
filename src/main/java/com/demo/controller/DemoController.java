package com.demo.controller;

import com.demo.service.DemoService;
import com.demo.vo.DemoResponesVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Demo
 *
 * <AUTHOR>
 * @date 2022-11-02  11:15
 */
@Controller
@RequestMapping("/demo")
@Slf4j
public class DemoController {

    @Autowired
    private DemoService demoService;

    @GetMapping
    public String view() {
        return "demo";
    }

    @GetMapping(value = "/key")
    public DemoResponesVO getByKey(@RequestParam Long id) {
        return demoService.getByKey(id);
    }

}
