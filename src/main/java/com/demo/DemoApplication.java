package com.demo;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
public class DemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }


//    public static void main(String[] args) {
//
//        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
//        /**
//         * #mysql
//         * jasypt.encryptor.password=mesopal
//         * OPAL_DB_USERNAME= ENC(A15zeTQez77wt0cipth+Us1uYUKnYJoE)
//         * OPAL_DB_PASSWORD= ENC(TUDQR0EbFW9p87fbt3PcXz0wQ87LEMym)
//         * spring.datasource.url= ENC(H78ZvvXA2cwQtB9iyHNeI4Yssqmk3u906HsA/a/YjSZtLM5nQG9z4WLqfv3ofz9m0qCbl0wIP0tC29TumGExMp68FbyGqPj1rb88sLuf9+2UQa0qDShgFCbHQcmtmUDyB0bOe39yq4EL+ly7bppixQuG6jMeSJAf/EZqAzqpXe0werTn8rUa38jqJpx1O2X2SQIJY9dvorPS6/+kAB8pkQ==)
//         */
//        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
//        // 用于设置加密密钥。密钥是用于加密和解密字符串的关键信息。
//        config.setPassword("mesopal");
//        // 加密算法的名称,jasypt-3.0.5版本后默认的加密方式
////        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
////        // 用于设置加密时迭代次数的数量，增加迭代次数可以使攻击者更难进行密码破解。
//        config.setKeyObtentionIterations("1000");
////        // 加密器池的大小。池是一组加密器实例，可确保加密操作的并发性。
////        config.setPoolSize("1");
////        // 用于设置JCE（Java Cryptography Extension）提供程序的名称。
////        config.setProviderName("SunJCE");
////        // 用于设置生成盐的类名称。在此配置中，我们使用了org.jasypt.salt.RandomSaltGenerator，表示使用随机生成的盐。
////        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
////        // 用于设置Jasypt使用的初始化向量（IV）生成器的类名。初始化向量是在加密过程中使用的一个固定长度的随机数，用于加密数据块，使每个数据块的加密结果都是唯一的。在此配置中，我们使用了org.jasypt.iv.RandomIvGenerator类，该类是一个随机生成器，用于生成实时随机IV的实例。这样可以确保每次加密的IV都是唯一的，从而增加加密强度。
////        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
////        // 指定加密输出类型。在此配置中，我们选择了base64输出类型。
//        config.setStringOutputType("base64");
//        encryptor.setConfig(config);
//
////        // 明文1
////        String name_encrypt = "root";
////        // 明文2
////        String password_encrypt = "123456";
////
////        // 明文加密
////        String encrypt1 = encryptor.encrypt(name_encrypt);
////        String encrypt2 = encryptor.encrypt(password_encrypt);
////        System.out.println("明文加密1：" + encrypt1);
////        System.out.println("明文加密2：" + encrypt2);
//
//        // 密文解密
//        String decrypt1 = encryptor.decrypt("A15zeTQez77wt0cipth+Us1uYUKnYJoE");
//        String decrypt2 = encryptor.decrypt("TUDQR0EbFW9p87fbt3PcXz0wQ87LEMym");
//        System.out.println("密文解密1：" + decrypt1);
//        System.out.println("密文解密2：" + decrypt2);
//    }

}
