package com.demo.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "t_demo")
public class DemoPO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("demo_str")
    private String demoStr;

    @TableField("demo_type")
    private Integer demoType;

    @TableField("demo_time")
    private Date demoTime;

}
