package com.demo.apache.log;


import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.apache.logging.log4j.core.config.Configurator;

import java.io.FileInputStream;
import java.io.IOException;

public class Log4j2Demo {

    public static void main(String[] args) throws IOException {
        String config=System.getProperty("user.dir");//获取程序的当前路径
        ConfigurationSource source = new ConfigurationSource(new FileInputStream(config+"\\java-demo\\src\\main\\resources\\log4j2.xml"));
        Configurator.initialize(null, source);
        Logger logger = LogManager.getLogger(Log4j2Demo.class);
        logger.error("测试error");
        logger.debug("测试debug");
    }

}
