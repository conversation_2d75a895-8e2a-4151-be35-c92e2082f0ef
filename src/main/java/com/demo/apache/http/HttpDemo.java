package com.demo.apache.http;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

/**
 * 依赖于Apache的httpclient包
 */
public class HttpDemo {

    public static void main(String[] args) throws Exception {
        String loginURL = "http://60.205.206.255:8081/OSN/api/getDetail/v1";
        String json = "{\"hsn\":\"jdcs\",\"sku\":[\"1260621\"]}";
        HttpPost httppost = new HttpPost(loginURL);
        StringEntity entity = new StringEntity(json, "utf-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httppost.setEntity(entity);
        HttpClient httpClient = new DefaultHttpClient();
        HttpResponse httpResponse = httpClient.execute(httppost);
        httppost.releaseConnection();
        String strResult = EntityUtils.toString(httpResponse.getEntity());
        System.out.println(strResult);
    }

}
