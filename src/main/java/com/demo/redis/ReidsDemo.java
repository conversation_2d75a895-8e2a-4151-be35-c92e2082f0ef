package com.demo.redis;

import redis.clients.jedis.Jedis;

public class ReidsDemo {

    public static void main(String[] args) {
        //连接本地的 Redis 服务
        Jedis jedis = new Jedis("127.0.0.1");
        // 如果 Redis 服务设置来密码，需要下面这行，没有就不需要
//        jedis.auth("root");
        String s = jedis.clientList();
        System.out.println(s);
        //查看服务是否运行
        System.out.println("服务正在运行: "+jedis.ping());
    }


}
