package com.demo.jdk.thread;

import org.junit.Test;

import java.util.concurrent.*;

public class ThreadMain {

    /**
     * 继承Thread
     */
    @Test
    public void threadTest() {
        new ThreadDemo().start();

        // 尾部代码块, 是对匿名内部类形式的语法糖
        new Thread() {
            @Override
            public void run() {
                System.out.println(Thread.currentThread().getName() + "\t" + Thread.currentThread().getId());
            }
        }.start();

    }

    /**
     * 实现Runnable接口
     */
    @Test
    public void runnablTest() {
        RunnableDemo threadDemo2 = new RunnableDemo();
        new Thread(threadDemo2).start();

        // 匿名内部类
        new Thread(new Runnable() {
            @Override
            public void run() {
                System.out.println(Thread.currentThread().getName() + "\t" + Thread.currentThread().getId());
            }
        }).start();

        // Runnable是函数式接口，所以可以使用Lamda表达式形式
        Runnable runnable = () -> {
            System.out.println(Thread.currentThread().getName() + "\t" + Thread.currentThread().getId());
        };
        new Thread(runnable).start();

    }

    /**
     * 实现Callable接口
     */
    @Test
    public void callableTest() throws ExecutionException, InterruptedException {
        // 将Callable包装成FutureTask，FutureTask也是一种Runnable
        CallableDemo callable = new CallableDemo();
        FutureTask<Integer> futureTask = new FutureTask<>(callable);
        new Thread(futureTask).start();
        // get方法会阻塞调用的线程
        Integer sum = futureTask.get();
        System.out.println(Thread.currentThread().getName() + Thread.currentThread().getId() + "=" + sum);
    }

    @Test
    public void threadPoolTest() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(1, 10, 5,
                TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(5));
        for (int i = 0; i < 10; i++) {
            //无返回结构线程使用 execute
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    System.out.println(Thread.currentThread().getName() + "\t" + Thread.currentThread().getId());
                    try {
                        Thread.sleep(10000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            });
            Future<Integer> future = executor.submit(new Callable<Integer>() {
                @Override
                public Integer call() throws Exception {
                    System.out.println(Thread.currentThread().getName() + "\t" + Thread.currentThread().getId());
                    try {
                        Thread.sleep(10000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 1;
                }
            });

        }
        System.out.println("线程池中线程数目："+executor.getPoolSize()+"，队列中等待执行的任务数目："+
                executor.getQueue().size()+"，已执行玩别的任务数目："+executor.getCompletedTaskCount());

    }

}
