package com.demo.jdk.base;

/**
 * 类加载
 */
public class MethodDemo {

    static {
        System.out.println("父类静态代码块");
    }

    MethodDemo () {
        System.out.println("无参构造防范");
    }

    MethodDemo (String str) {
        System.out.println("有参构造方法");
    }

    public void test(String ... strs) {
        System.out.println(strs[0]);
    }

    public static void main(String[] args) {
        new MethodDemo().test("1","2");
    }
}
