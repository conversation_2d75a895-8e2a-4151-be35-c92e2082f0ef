package com.demo.jdk.base;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 日历 jdk8
 */
public class LocalDateDemo {

    public  void main(String[] args) {
        LocalDate now = LocalDate.now();
        System.out.println(now.toString());
        int monthValue = now.getMonthValue();//当前月
        System.out.println(monthValue);
        int dayOfMonth = now.getDayOfMonth();//当前日
        System.out.println(dayOfMonth);
        LocalDate minusDay = now.minusDays(dayOfMonth-1);//向前推天数
        System.out.println(minusDay);
        DayOfWeek dayOfWeek = minusDay.getDayOfWeek();
        int week = dayOfWeek.getValue();//星期几
        System.out.println(week);

        System.out.println("Mon Tue Wed Thu Fri Sat Sun");
        for (int i = 1; i < week; i++) {
            System.out.print("    ");
        }
        while (minusDay.getMonthValue() == monthValue) {
            System.out.printf("%3d",minusDay.getDayOfMonth());
            if (minusDay.getDayOfMonth() == dayOfMonth) {
                System.out.print("*");
            } else {
                System.out.print(" ");
            }
            minusDay = minusDay.plusDays(1);
            if (minusDay.getDayOfWeek().getValue() == 1) {
                System.out.println();
            }
        }
        if (minusDay.getDayOfWeek().getValue() != 1) {
            System.out.println();
        }


        LocalTime now1 = LocalTime.now();
        System.out.println(now1);
    }

}
