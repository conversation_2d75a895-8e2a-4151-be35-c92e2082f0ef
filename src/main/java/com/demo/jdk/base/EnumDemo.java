package com.demo.jdk.base;

public enum EnumDemo {

    one(1,"一");
    EnumDemo(int i,String s) {
        this.i = i;
        this.s = s;
    }
    private int i;

    private String s;

    public int getI() {
        return i;
    }

    public void setI(int i) {
        this.i = i;
    }

    public String getS() {
        return s;
    }

    public void setS(String s) {
        this.s = s;
    }

    /**
     * 根据 i 获取 s
     * @param i
     * @return
     */
    public static String getS(int i) {
        String s = null;
        EnumDemo[] enumDemos = EnumDemo.values();
        for (EnumDemo enumDemo : enumDemos) {
            int code = enumDemo.getI();
            if (code == i) {
                s = enumDemo.getS();
            }
        }
        return s;
    }


}
