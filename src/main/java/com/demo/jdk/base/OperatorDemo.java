package com.demo.jdk.base;

/**
 * 运算符
 *
 * <AUTHOR>
 * @date 2023-01-10  15:51
 */
public class OperatorDemo {

    public static void xor() {
        int a = 12;
        int b = 24;
        /*
        异或运算是对二进制数进行无进位相加，相同位数上的值相同为0不同为1
         * 异或运算交换变量的值，a 和 b 不在同一内存才能交换，同一内存的值会洗成0
         */
        a = a ^ b;
        b = a ^ b;
        a = a ^ b;
        System.out.println(a);
        System.out.println(b);
        //在一个数组中，已知只有一种数出现了奇数次，其他的所有数都出现了偶数次，请问怎么找到奇数次的数。
        int eor = 0;
        int[] arrays1 = {2,2,2,2,1,1,1,1,3,3,3};//一种数
        for (int array : arrays1) {
            eor = eor ^ array;
        }
        //两种数出现了奇数次，请问怎么找到奇数次的数。
        int eor01 = 0;
        int eor02 = 0;
        int[] arrays2 = {2,2,2,2,1,1,1,1,3,3,3,6,6,6};//一种数
        for (int array : arrays2) {
            eor = eor ^ array;
        }

    }

    public static void main(String[] args) {
        xor();
    }
}
