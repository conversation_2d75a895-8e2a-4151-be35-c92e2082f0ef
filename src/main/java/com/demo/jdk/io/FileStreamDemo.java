package com.demo.jdk.io;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

public class FileStreamDemo {

    public void fileStream() {
        try {
            String url = "D:\\";
            File file = new File(url + "a\\file.txt");//实例化文件流
            /* 创建一级目录，上级路径不存在不创建，不报错 */
            boolean mkdir = file.mkdir();
            /* 创建多级目录 */
            boolean mkdirs = file.mkdirs();
            /* 在已有路径下创建文件，路径不存在时报错*/
            file.createNewFile();
            /* 删除有返回值，能判断是否成功 */
            boolean delete = file.delete();
            /* 在程序退出时删除文件 */
            file.deleteOnExit();
            /* 文件是否存在 */
            boolean exists = file.exists();
            /*判断是否可执行*/
            boolean b = file.canExecute();
            /*是否是文件*/
            boolean isFile = file.isFile();
            /*是否是文件夹*/
            boolean isDirectory = file.isDirectory();
            /*java能得到文件中的隐藏文件但是对隐藏文件时不能访问的*/
            boolean hidden = file.isHidden();
            /*绝对路径即时不存在也能得到。*/
            boolean absolute = file.isAbsolute();
            /*获取信息*/
            String name = file.getName();
            String path = file.getPath();
            String parent = file.getParent();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void getHeader() throws IOException {
        byte[] b = new byte[28];
        FileInputStream inputStream = new FileInputStream("C:\\_file\\_pcitc\\nacos.dmp");
        inputStream.read(b, 0, 28);
        inputStream.close();
        String key = bytes2hex(b);
        System.out.println(key);
    }


    /**
     * 将字节数组转换成16进制字符串
     */
    private static String bytes2hex(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }


    public static void main(String[] args) throws IOException {
        FileStreamDemo fileStreamDemo = new FileStreamDemo();
        fileStreamDemo.getHeader();
    }

}
