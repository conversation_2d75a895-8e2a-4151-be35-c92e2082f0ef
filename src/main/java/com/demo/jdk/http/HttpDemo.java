package com.demo.jdk.http;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * JDK原生
 */
public class HttpDemo {


    public static void main(String[] args) throws Exception {
        //http://47.95.223.66:8001
        URL url = new URL("http://60.205.206.255:8081/OSN/api/getDetail/v1");
        HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
        //设置连接方式
        urlConnection.setRequestMethod("POST");
        //设置连接主机服务器的超时时间：15000毫秒
        urlConnection.setConnectTimeout(15000);
        // 设置读取远程返回的数据时间：60000毫秒
        urlConnection.setReadTimeout(60000);
        //设置请求Headers
        urlConnection.setRequestProperty("Server", "Apache-Coyote/1.1");
        urlConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        //设置是否从httpUrlConnection读出，默认情况下是false; 如包含请求参数是设置为true;
        urlConnection.setDoOutput(true);

        OutputStream outputStream = urlConnection.getOutputStream();
        String json = "{\"hsn\":\"jdcs\",\"sku\":\"1260621\"}";
        byte[] bytes = json.getBytes();
        outputStream.write(bytes);

        InputStream inputStream = urlConnection.getInputStream();
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        String s = bufferedReader.readLine();
        System.out.println(s);
    }

}
