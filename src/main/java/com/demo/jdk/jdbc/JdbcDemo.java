package com.demo.jdk.jdbc;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class JdbcDemo {

    public static void main(String[] age) throws Exception {
        //1.加载驱动
        Class.forName("oracle.jdbc.OracleDriver");
        //2.建立连接
        String url = "**********************************************";
        String user = "root";
        String password = "123456";
        Connection connection = DriverManager.getConnection(url,user,password);
        //3.执行SQL
        String sql = "select * from T_PM_AREA";
        Statement statement = connection.createStatement();
        ResultSet resultSet = statement.executeQuery(sql);
        //4.获取结果
        while (resultSet.next()) {
            System.out.println(resultSet.getObject(1));
        }
    }
}
