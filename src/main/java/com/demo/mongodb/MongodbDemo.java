package com.demo.mongodb;

import com.mongodb.MongoClient;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;

import java.util.Collections;

public class MongodbDemo {

    public static void main(String[] args) {
        try {
            ServerAddress serverAddress = new ServerAddress("127.0.0.1", 27017);
            String password = "123456";
            MongoCredential credential = MongoCredential.createScramSha1Credential("root",
                     "demo",password.toCharArray());
            // 连接到 mongodb 服务
            MongoClient mongoClient = new MongoClient(serverAddress, Collections.singletonList(credential));
            // 连接到数据库
            MongoDatabase mongoDatabase = mongoClient.getDatabase("demo");

            MongoCollection<Document> collection = mongoDatabase.getCollection("demo");
            Document document = new Document("str", "Hello World!");
            collection.insertOne(document);
            System.out.println("Connect to database successfully");

        } catch (Exception e) {
            System.err.println(e.getClass().getName() + ": " + e.getMessage());
        }
    }
}
