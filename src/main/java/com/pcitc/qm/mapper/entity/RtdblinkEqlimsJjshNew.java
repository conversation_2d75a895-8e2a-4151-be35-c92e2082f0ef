package com.pcitc.qm.mapper.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_RTDBLINK_EQLIMS_JJSH_NEW")
public class RtdblinkEqlimsJjshNew implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 采样点编码
     */
    @TableField("SAMPLE_POINT_CODE")
    private String samplePointCode;

    /**
     * 采样点名称
     */
    @TableField("SAMPLE_POINT_NAME")
    private String samplePointName;

    /**
     * 物料编码
     */
    @TableField("MATERIAL_CODE")
    private String materialCode;

    /**
     * 物料名称
     */
    @TableField("MATERIAL_NAME")
    private String materialName;

    /**
     * 样品名称
     */
    @TableField("SAMPLE_NAME")
    private String sampleName;

    /**
     * 分析组分编码
     */
    @TableField("ANLYCOM_CODE")
    private String anlycomCode;

    /**
     * 分析组分名称
     */
    @TableField("ANLYCOM_NAME")
    private String anlycomName;

    /**
     * 采样时间
     */
    @TableField("SAMPLE_TIME")
    private Date sampleTime;

    /**
     * 分析值
     */
    @TableField("ANLY_VALUE")
    private String anlyValue;

    /**
     * 分析类型
     */
    @TableField("ANLY_TYPE")
    private String anlyType;

    /**
     * 批次判断结果: 合格；不合格
     */
    @TableField("JUDGE_RESULT")
    private String judgeResult;

    /**
     * 组分检验结果：F 为合格；T为不合格 
     */
    @TableField("ANLYCOM_FLG")
    private String anlycomFlg;

    /**
     * 批次号
     */
    @TableField("BATCH")
    private String batch;

    /**
     * 数量
     */
    @TableField("IN_QTY")
    private String inQty;

    /**
     * 供应商名称
     */
    @TableField("SUPPLIER")
    private String supplier;

    /**
     * 运单号
     */
    @TableField("VEHICLE_NUM")
    private String vehicleNum;

    /**
     * 运输方式
     */
    @TableField("TRANS_TYPE")
    private String transType;

    /**
     * 产量
     */
    @TableField("YIELD_QTY")
    private String yieldQty;

    /**
     * 质量等级
     */
    @TableField("QLY_GRADE")
    private String qlyGrade;

    /**
     * 合格证地址
     */
    @TableField("URL")
    private String url;

    /**
     * 检验人
     */
    @TableField("EXAM_USER")
    private String examUser;

    /**
     * 检验时间
     */
    @TableField("EXAM_TIME")
    private Date examTime;

    /**
     * 提交时间
     */
    @TableField("APPROVAL_TIME")
    private Date approvalTime;

    /**
     * 合格范围
     */
    @TableField("MV_RANGE")
    private String mvRange;

    /**
     * 样品标准号
     */
    @TableField("M_U_STANDARDS")
    private String mUStandards;

    /**
     * 样品类型
     */
    @TableField("S_SAMPLE_TYPE")
    private String sSampleType;

    /**
     * 计量单位
     */
    @TableField("R_UNITS")
    private String rUnits;


}
