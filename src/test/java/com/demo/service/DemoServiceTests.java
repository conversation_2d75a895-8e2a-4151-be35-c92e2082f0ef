package com.demo.service;

import com.demo.DemoApplication;
import org.jasypt.encryption.StringEncryptor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {DemoApplication.class})
public class DemoServiceTests {


    @Resource
    private StringEncryptor stringEncryptor;

    @Test
    public void encodeMysql() {
        System.out.println( "mysql密码加密密文：" + stringEncryptor.encrypt("123456") );
        System.out.println("解密密文：" + stringEncryptor.decrypt("zuLk5G5ZrjHoJSibRY2uM0r0c/WQ2PnMHedRR1uxVnzE+v367TV3HeOU/IQ/cHZ1b9YDV/YK/Jl8cAoV34X1DHSNsRN3krbm3V1/MIyqOjGX3ZVYO9vFF24QAEdcFdNQtuvKJo1OUgG+5D87e4Gpuszcu5f0EVguF4puif9p+H6eVHexnI5PqMqQs871+4DxlFL+UXQItEA="));
        System.out.println("解密密文：" + stringEncryptor.decrypt("ZjshvMtOOfIkoTjyRpii1UlTZatgRUC9"));
    }


}
