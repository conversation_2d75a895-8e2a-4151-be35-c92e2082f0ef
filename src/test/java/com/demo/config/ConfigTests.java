package com.demo.config;

import com.demo.DemoApplication;
import com.demo.bean.DemoBean;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2023-01-05  14:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {DemoApplication.class})
public class ConfigTests {

    @Autowired
    private DemoBean demoBean;

    @Test
    public void getDemoBean() {
        Long id = demoBean.getId();
        System.out.println(id);
    }


}
