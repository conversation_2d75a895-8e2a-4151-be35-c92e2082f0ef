<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.8</version>
    </parent>

    <groupId>com.demo</groupId>
    <artifactId>springboot-demo</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- 指定资源文件复制过程中采用的编码方式 -->
        <encoding>UTF-8</encoding>
<!--        <jdbc.username>root</jdbc.username>-->
<!--        <jdbc.password>123456</jdbc.password>-->
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-logging</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>3.0.5</version>
        </dependency>
        <!-- Kafka -->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.kafka</groupId>-->
        <!--            <artifactId>spring-kafka</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.kafka</groupId>-->
        <!--            <artifactId>spring-kafka-test</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <!-- Shiro 权限控制框架 -->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.shiro</groupId>-->
        <!--            <artifactId>shiro-spring</artifactId>-->
        <!--            <version>1.7.1</version>-->
        <!--        </dependency>-->
        <!-- alibaba fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.47</version>
        </dependency>
        <!-- Maven 示例 -->
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>6.4.4.Final</version>
        </dependency>
        <dependency>
            <groupId>jakarta.persistence</groupId>
            <artifactId>jakarta.persistence-api</artifactId>
            <version>3.1.0</version>
        </dependency>
        <!-- mybatis plus  generator -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.1</version>
        </dependency>
        <!-- mybatis plus  -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>
        <!-- Druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.14</version>
        </dependency>
        <!-- mongodb -->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-data-mongodb</artifactId>-->
        <!--        </dependency>-->
        <!-- TDengine -->
        <!--        <dependency>-->
        <!--            <groupId>com.taosdata.jdbc</groupId>-->
        <!--            <artifactId>taos-jdbcdriver</artifactId>-->
        <!--            <version>3.0.0</version>-->
        <!--        </dependency>-->
        <!-- 达梦数据库 -->
<!--        <dependency>-->
<!--            <groupId>com.dameng</groupId>-->
<!--            <artifactId>Dm8JdbcDriver18</artifactId>-->
<!--            <version>8.1.1.49</version>-->
<!--        </dependency>-->
        <!-- mysql 数据源 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.31</version>
        </dependency>
        <!-- log4j2 日志框架 -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-log4j2</artifactId>-->
<!--        </dependency>-->
        <!-- 使用Thymaleaf视图模板引擎 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-logging</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>2.1.1</version>
        </dependency>
        <!-- Spring AOP -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-logging</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-logging</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <build>
        <finalName>springboot-demo</finalName>
<!--        <resources>-->
<!--            <resource>-->
<!--                &lt;!&ndash; 指定资源文件的目录 &ndash;&gt;-->
<!--                <directory>${project.basedir}/src/main/resources</directory>-->
<!--                &lt;!&ndash; 是否开启过滤替换配置，默认是不开启的 &ndash;&gt;-->
<!--                <filtering>true</filtering>-->
<!--                &lt;!&ndash; **匹配任意深度的文件路径，*匹配任意个字符。 &ndash;&gt;-->
<!--                &lt;!&ndash; 列出需要被处理 &ndash;&gt;-->
<!--                <includes>-->
<!--                    <include>**/application.yml</include>-->
<!--                    <include>**/log4j2.xml</include>-->
<!--                </includes>-->
<!--                &lt;!&ndash; 排除需要被处理的资源文件列表 &ndash;&gt;-->
<!--                <excludes>-->
<!--                    <exclude>**/loacl.properties</exclude>-->
<!--                </excludes>-->
<!--            </resource>-->
<!--        </resources>-->
<!--        <testResources>-->
<!--            <testResource>-->
<!--                &lt;!&ndash; 指定资源文件的目录 &ndash;&gt;-->
<!--                <directory>${project.basedir}/src/test/resources</directory>-->
<!--                &lt;!&ndash; 是否开启过滤替换配置，默认是不开启的 &ndash;&gt;-->
<!--                <filtering>true</filtering>-->
<!--            </testResource>-->
<!--        </testResources>-->
        <plugins>
            <!--添加配置跳过测试-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 定义多套环境配置信息 -->
<!--    <profiles>-->
<!--        <profile>-->
<!--            &lt;!&ndash; mvn package -P环境配置id。可以使用多个环境配置id,但有相同属时后面的会覆盖前面的 &ndash;&gt;-->
<!--            <id>dev</id>-->
<!--            <activation>-->
<!--                &lt;!&ndash; 自定义的属性值来控制 mvn package -D属性xx=属性xx的值 &ndash;&gt;-->
<!--                <property>-->
<!--                    <name>env</name>-->
<!--                    <value>dev</value>-->
<!--                </property>-->
<!--                &lt;!&ndash; 开启默认环境配置 &ndash;&gt;-->
<!--                <activeByDefault>true</activeByDefault>-->
<!--            </activation>-->
<!--            &lt;!&ndash;            <properties>&ndash;&gt;-->
<!--            &lt;!&ndash;                <jdbc.username>local</jdbc.username>&ndash;&gt;-->
<!--            &lt;!&ndash;                <jdbc.password>123456</jdbc.password>&ndash;&gt;-->
<!--            &lt;!&ndash;            </properties>&ndash;&gt;-->
<!--            <build>-->
<!--                <filters>-->
<!--                    &lt;!&ndash; xx.properties文件路径（相对路径或者完整路径）&ndash;&gt;-->
<!--                    <filter>${project.basedir}/src/main/resources/local.properties</filter>-->
<!--                </filters>-->
<!--            </build>-->
<!--        </profile>-->
<!--    </profiles>-->

</project>