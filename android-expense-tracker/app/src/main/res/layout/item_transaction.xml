<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Type indicator -->
        <View
            android:id="@+id/typeIndicator"
            android:layout_width="4dp"
            android:layout_height="0dp"
            android:background="@color/income_green"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Category and Description -->
        <TextView
            android:id="@+id/tvCategory"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tvAmount"
            app:layout_constraintStart_toEndOf="@+id/typeIndicator"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="餐饮" />

        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:textColor="@color/dark_gray"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/tvAmount"
            app:layout_constraintStart_toEndOf="@+id/typeIndicator"
            app:layout_constraintTop_toBottomOf="@+id/tvCategory"
            tools:text="午餐" />

        <TextView
            android:id="@+id/tvDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:textColor="@color/dark_gray"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/tvAmount"
            app:layout_constraintStart_toEndOf="@+id/typeIndicator"
            app:layout_constraintTop_toBottomOf="@+id/tvDescription"
            tools:text="2023-08-25" />

        <!-- Amount -->
        <TextView
            android:id="@+id/tvAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="-¥25.00"
            tools:textColor="@color/expense_red" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
