<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>VUE模版语法</title>
    <script type="text/javascript" src="vue.js"></script>
</head>
<body>
<!--
Vue模板语法有2人类:
1.插值语法示
功能:用于解析标签体内容。
写法: {{xxx}}，xxx是js表达式，且可以直接谈取到data中的所有属性.
2.指令语法:
功能:用于解析标签(包括:标签属性、标签体内容、绑定事件.....)。
举例:v-bind;href="xxx"或 简写为 :href="xxx"，xxx同样变写js表达式且可以直接读取到data中的所有属性。
备注:Vue中有很多的指令，且形式都是:v-????，此处我们只是拿v-bind举个例子。
 -->
<div id="root">
    <h1>插值语法</h1>
    <h2>{{str}}</h2>
    <hr/>
    <h1>指令语法</h1>
    <!-- v-bind: 可以简写成 ':' -->
    <a :href="url">百度</a>
    <hr/>
</div>
<script>
    //Vue.config.productionTip=false //以阻止 vue 在启动时生成生产提示。 (20230915-无作用)

    /* 创建Vue实例 */
    const x = new Vue({
        el: '#root', //el (element) 用于指定当前Vue实例为那个容器服务，值为css选择器字符串。
        data: { //data 用于存储数据，提供给el指定的容器去使用。
            str: 'Hello World!',
            url: 'https://www.baidu.com'
        }
    })
</script>
</body>
</html>