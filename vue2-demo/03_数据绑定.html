<!DOCTYPE html>
<html lang="zh" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <title>数据绑定</title>
    <script type="text/javascript" src="vue.js"></script>
</head>
<body>
<div id="root">
<!--
Vue中有2种数据绑定的方式:
1.单向绑定(v-bind): 数据只能从data流向页面。
2.双向绑定(v-model): 数据不仅能从data流向页面，还可以从页面流向data。
备注:
1.双向绑定一般都应用在表单类元素上(如: input、select等)
2.v-model:value 可以简写为 v-model，因为v-model默认收集的就是value值。
-->
    单向数据绑定<input type="text" v-bind:value="str"></br>
    双向数据绑定<input type="text" v-model:value="str">
</div>
<script>
    Vue.config.productionTip = false //以阻止 vue 在启动时生成生产提示。 (20230915-无作用)

    /* 创建Vue实例 */
    const x = new Vue({
        el: '#root', //el (element) 用于指定当前Vue实例为那个容器服务，值为css选择器字符串。
        data: { //data 用于存储数据，提供给el指定的容器去使用。
            str: 'Hello World'
        }
    })
</script>
</body>
</html>